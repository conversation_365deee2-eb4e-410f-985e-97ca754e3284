"""
数据库连接和模型基类
"""

from sqlalchemy import create_engine, MetaData, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool
import asyncpg
import asyncio
from typing import AsyncGenerator

from .config import settings, DatabaseConfig


# 同步数据库引擎
if settings.DATABASE_URL.startswith("sqlite"):
    engine = create_engine(
        DatabaseConfig.get_database_url(),
        poolclass=StaticPool,
        connect_args={"check_same_thread": False},
        echo=settings.DEBUG
    )
else:
    engine = create_engine(
        DatabaseConfig.get_database_url(),
        pool_pre_ping=True,
        pool_recycle=300,
        echo=settings.DEBUG
    )

# 会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 元数据
metadata = MetaData()

# 模型基类
Base = declarative_base(metadata=metadata)


def get_db():
    """获取数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


# 异步数据库连接池
class AsyncDatabase:
    """异步数据库连接管理器"""
    
    def __init__(self):
        self.pool = None
    
    async def connect(self):
        """建立异步数据库连接池"""
        if settings.DATABASE_URL.startswith("sqlite"):
            # SQLite不支持异步连接池，跳过
            return
        if not self.pool:
            self.pool = await asyncpg.create_pool(
                DatabaseConfig.get_async_database_url(),
                min_size=1,
                max_size=10
            )
    
    async def disconnect(self):
        """关闭异步数据库连接池"""
        if self.pool:
            await self.pool.close()
            self.pool = None
    
    async def execute(self, query: str, *args):
        """执行SQL语句"""
        async with self.pool.acquire() as connection:
            return await connection.execute(query, *args)
    
    async def fetch(self, query: str, *args):
        """查询多行数据"""
        async with self.pool.acquire() as connection:
            return await connection.fetch(query, *args)
    
    async def fetchrow(self, query: str, *args):
        """查询单行数据"""
        async with self.pool.acquire() as connection:
            return await connection.fetchrow(query, *args)
    
    async def fetchval(self, query: str, *args):
        """查询单个值"""
        async with self.pool.acquire() as connection:
            return await connection.fetchval(query, *args)


# 全局异步数据库实例
async_db = AsyncDatabase()


# 数据库初始化
def init_db():
    """初始化数据库表"""
    Base.metadata.create_all(bind=engine)


# 数据库健康检查
def check_db_health() -> bool:
    """检查数据库连接健康状态"""
    try:
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        return True
    except Exception as e:
        print(f"Database health check failed: {e}")
        return False


async def check_async_db_health() -> bool:
    """检查异步数据库连接健康状态"""
    # SQLite不支持异步连接，直接返回True
    if settings.DATABASE_URL.startswith("sqlite"):
        return True

    try:
        if not async_db.pool:
            await async_db.connect()
        result = await async_db.fetchval("SELECT 1")
        return result == 1
    except Exception:
        return False


def init_database_with_data():
    """初始化数据库并创建初始数据"""
    from .models import User, Company  # 延迟导入避免循环依赖

    # 创建表
    Base.metadata.create_all(bind=engine)

    # 创建初始数据
    db = SessionLocal()
    try:
        # 检查是否已有用户数据
        existing_user = db.query(User).first()
        if not existing_user:
            # 创建默认用户
            default_user = User(
                id="1",
                username="admin",
                email="<EMAIL>",
                hashed_password="$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW",  # "admin"
                full_name="系统管理员",
                is_active=True
            )
            db.add(default_user)

            # 创建示例公司
            sample_companies = [
                Company(
                    id="1",
                    name="阿里巴巴集团",
                    code="BABA",
                    industry="电商/云计算",
                    description="全球领先的电子商务和云计算公司",
                    address="浙江省杭州市余杭区文一西路969号",
                    contact_person="张三",
                    contact_phone="0571-85022088",
                    contact_email="<EMAIL>",
                    created_by="1",
                    is_active=True
                ),
                Company(
                    id="2",
                    name="腾讯控股",
                    code="TCEHY",
                    industry="互联网服务",
                    description="中国领先的互联网增值服务提供商",
                    address="广东省深圳市南山区科技中一路腾讯大厦",
                    contact_person="李四",
                    contact_phone="0755-86013388",
                    contact_email="<EMAIL>",
                    created_by="1",
                    is_active=True
                ),
                Company(
                    id="3",
                    name="比亚迪股份",
                    code="BYD",
                    industry="新能源汽车",
                    description="全球领先的新能源汽车制造商",
                    address="广东省深圳市坪山区比亚迪路3009号",
                    contact_person="王五",
                    contact_phone="0755-89888888",
                    contact_email="<EMAIL>",
                    created_by="1",
                    is_active=True
                )
            ]

            for company in sample_companies:
                db.add(company)

            db.commit()
            print("✅ 初始数据创建完成")
        else:
            print("✅ 数据库已存在数据，跳过初始化")

    except Exception as e:
        print(f"❌ 初始化数据失败: {e}")
        db.rollback()
    finally:
        db.close()

    print("✅ 数据库初始化完成")
