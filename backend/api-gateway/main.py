"""
LinkFin API网关
统一入口，负责路由转发、认证授权、限流等
"""

import sys
import os
from pathlib import Path

# 添加共享库路径
sys.path.append(str(Path(__file__).parent.parent / "shared"))

from fastapi import FastAPI, Request, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from fastapi.openapi.docs import get_swagger_ui_html
from fastapi.openapi.utils import get_openapi
import uvicorn
import time
import logging
from contextlib import asynccontextmanager

from config import settings
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from shared.database import init_db, check_db_health
from middleware import (
    LoggingMiddleware,
    RateLimitMiddleware,
    AuthMiddleware,
    PrometheusMiddleware
)
from routers import auth, companies, reports, shares, health
from utils import json_serializer


# 配置日志
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("Starting LinkFin API Gateway...")
    
    # 初始化数据库
    try:
        init_db()
        logger.info("Database initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        raise
    
    # 检查数据库连接
    if not check_db_health():
        logger.error("Database health check failed")
        raise HTTPException(status_code=500, detail="Database connection failed")
    
    logger.info("API Gateway started successfully")
    
    yield
    
    # 关闭时执行
    logger.info("Shutting down LinkFin API Gateway...")


# 创建FastAPI应用
app = FastAPI(
    title="LinkFin API Gateway",
    description="财务报表智能分析系统API网关",
    version=settings.APP_VERSION,
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
    openapi_url="/openapi.json" if settings.DEBUG else None,
    lifespan=lifespan
)

# 添加中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["*"] if settings.DEBUG else ["localhost", "127.0.0.1", settings.SHARE_DOMAIN]
)

# 自定义中间件
app.add_middleware(LoggingMiddleware)
app.add_middleware(RateLimitMiddleware)
app.add_middleware(AuthMiddleware)
app.add_middleware(PrometheusMiddleware)


# 全局异常处理
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """HTTP异常处理"""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": True,
            "message": exc.detail,
            "status_code": exc.status_code,
            "timestamp": time.time(),
            "path": str(request.url.path)
        }
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """通用异常处理"""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    
    return JSONResponse(
        status_code=500,
        content={
            "error": True,
            "message": "Internal server error" if not settings.DEBUG else str(exc),
            "status_code": 500,
            "timestamp": time.time(),
            "path": str(request.url.path)
        }
    )


# 注册路由
app.include_router(health.router, prefix="/health", tags=["健康检查"])
app.include_router(auth.router, prefix="/api/v1/auth", tags=["认证"])
app.include_router(companies.router, prefix="/api/v1/companies", tags=["公司管理"])
app.include_router(reports.router, prefix="/api/v1/reports", tags=["报告管理"])
app.include_router(shares.router, prefix="/api/v1/shares", tags=["分享管理"])


@app.get("/", include_in_schema=False)
async def root():
    """根路径"""
    return {
        "message": "LinkFin API Gateway",
        "version": settings.APP_VERSION,
        "status": "running",
        "timestamp": time.time()
    }


@app.get("/info", tags=["系统信息"])
async def get_system_info():
    """获取系统信息"""
    return {
        "app_name": settings.APP_NAME,
        "version": settings.APP_VERSION,
        "environment": settings.APP_ENV,
        "debug": settings.DEBUG,
        "timestamp": time.time()
    }


# 自定义OpenAPI文档
def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema
    
    openapi_schema = get_openapi(
        title="LinkFin API Gateway",
        version=settings.APP_VERSION,
        description="""
        ## LinkFin 财务报表智能分析系统 API

        ### 功能特性
        - 🔐 用户认证与授权
        - 🏢 公司信息管理
        - 📊 财务数据处理
        - 📈 智能分析计算
        - 📄 报告生成与分享
        - 🔗 安全链接分享

        ### 认证方式
        使用JWT Bearer Token进行认证：
        ```
        Authorization: Bearer <your_token>
        ```

        ### 错误码说明
        - 400: 请求参数错误
        - 401: 未认证或认证失败
        - 403: 权限不足
        - 404: 资源不存在
        - 429: 请求频率限制
        - 500: 服务器内部错误
        """,
        routes=app.routes,
    )
    
    # 添加安全定义
    openapi_schema["components"]["securitySchemes"] = {
        "BearerAuth": {
            "type": "http",
            "scheme": "bearer",
            "bearerFormat": "JWT"
        }
    }
    
    app.openapi_schema = openapi_schema
    return app.openapi_schema


app.openapi = custom_openapi


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.API_HOST,
        port=settings.API_PORT,
        reload=settings.DEBUG,
        workers=1 if settings.DEBUG else settings.API_WORKERS,
        log_level=settings.LOG_LEVEL.lower()
    )
