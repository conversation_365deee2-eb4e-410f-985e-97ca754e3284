"""
公司管理路由
"""

import sys
import os
from typing import List, Optional
from datetime import datetime

# 添加共享库路径
sys.path.append(os.path.join(os.path.dirname(__file__), "..", "..", "shared"))

from fastapi import APIRouter, Depends, HTTPException, status, Request, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from shared.database import get_db
from models import Company, User
from utils import generate_uuid

router = APIRouter()


# Pydantic模型
class CompanyCreate(BaseModel):
    name: str
    code: Optional[str] = None
    industry: Optional[str] = None
    description: Optional[str] = None
    address: Optional[str] = None
    contact_person: Optional[str] = None
    contact_phone: Optional[str] = None
    contact_email: Optional[str] = None


class CompanyUpdate(BaseModel):
    name: Optional[str] = None
    industry: Optional[str] = None
    description: Optional[str] = None
    address: Optional[str] = None
    contact_person: Optional[str] = None
    contact_phone: Optional[str] = None
    contact_email: Optional[str] = None


class CompanyResponse(BaseModel):
    id: int
    uuid: str
    name: str
    code: Optional[str]
    industry: Optional[str]
    description: Optional[str]
    is_active: bool
    created_at: datetime

    class Config:
        from_attributes = True


def get_current_user_id(request: Request) -> str:
    """获取当前用户ID"""
    # 从请求头获取用户ID，这里简化处理，实际应该从JWT token中解析
    user_id = request.headers.get("X-User-ID")
    if not user_id:
        # 如果没有用户ID，返回默认用户ID（用于测试）
        user_id = "1"  # 默认用户ID
    return user_id


@router.get("/", response_model=List[CompanyResponse])
async def get_companies(
    request: Request,
    db: Session = Depends(get_db),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000)
):
    """获取公司列表"""
    
    user_id = get_current_user_id(request)
    
    companies = db.query(Company).filter(
        Company.created_by == user_id,
        Company.is_active == True
    ).offset(skip).limit(limit).all()
    
    return [CompanyResponse.from_orm(company) for company in companies]


@router.post("/", response_model=CompanyResponse)
async def create_company(
    company_data: CompanyCreate,
    request: Request,
    db: Session = Depends(get_db)
):
    """创建公司"""
    
    user_id = get_current_user_id(request)
    
    # 检查公司代码是否已存在
    if company_data.code:
        existing = db.query(Company).filter(Company.code == company_data.code).first()
        if existing:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="公司代码已存在"
            )
    
    # 创建公司
    company = Company(
        **company_data.dict(),
        created_by=user_id
    )
    
    db.add(company)
    db.commit()
    db.refresh(company)
    
    return CompanyResponse.from_orm(company)


@router.get("/{company_id}", response_model=CompanyResponse)
async def get_company(
    company_id: str,
    request: Request,
    db: Session = Depends(get_db)
):
    """获取公司详情"""

    user_id = get_current_user_id(request)

    company = db.query(Company).filter(
        Company.id == company_id,
        Company.created_by == user_id,
        Company.is_active == True
    ).first()

    if not company:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="公司不存在"
        )

    return CompanyResponse.from_orm(company)


@router.put("/{company_id}", response_model=CompanyResponse)
async def update_company(
    company_id: str,
    company_data: CompanyUpdate,
    request: Request,
    db: Session = Depends(get_db)
):
    """更新公司信息"""

    user_id = get_current_user_id(request)

    company = db.query(Company).filter(
        Company.id == company_id,
        Company.created_by == user_id,
        Company.is_active == True
    ).first()

    if not company:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="公司不存在"
        )

    # 更新字段
    for field, value in company_data.dict(exclude_unset=True).items():
        setattr(company, field, value)

    db.commit()
    db.refresh(company)

    return CompanyResponse.from_orm(company)


@router.delete("/{company_id}")
async def delete_company(
    company_id: str,
    request: Request,
    db: Session = Depends(get_db)
):
    """删除公司（软删除）"""

    user_id = get_current_user_id(request)

    company = db.query(Company).filter(
        Company.id == company_id,
        Company.created_by == user_id,
        Company.is_active == True
    ).first()

    if not company:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="公司不存在"
        )

    # 软删除
    company.is_active = False
    db.commit()

    return {"message": "公司删除成功"}
