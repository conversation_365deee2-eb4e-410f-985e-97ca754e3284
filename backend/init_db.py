#!/usr/bin/env python3
"""
数据库初始化脚本
运行此脚本来创建数据库表和初始数据
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 简化的数据库初始化
from sqlalchemy import create_engine, Column, String, Boolean, DateTime, Text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import uuid

# 数据库配置
DATABASE_URL = "sqlite:///./linkfin.db"
engine = create_engine(DATABASE_URL, connect_args={"check_same_thread": False})
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# 简化的模型定义
class User(Base):
    __tablename__ = "users"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    username = Column(String, unique=True, nullable=False)
    email = Column(String, unique=True, nullable=False)
    hashed_password = Column(String, nullable=False)
    full_name = Column(String)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)

class Company(Base):
    __tablename__ = "companies"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String, nullable=False)
    code = Column(String, unique=True, nullable=False)
    industry = Column(String)
    description = Column(Text)
    address = Column(String)
    contact_person = Column(String)
    contact_phone = Column(String)
    contact_email = Column(String)
    created_by = Column(String, nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)

def init_database():
    """初始化数据库"""
    # 创建表
    Base.metadata.create_all(bind=engine)

    # 创建初始数据
    db = SessionLocal()
    try:
        # 检查是否已有用户数据
        existing_user = db.query(User).first()
        if not existing_user:
            # 创建默认用户
            default_user = User(
                id="1",
                username="admin",
                email="<EMAIL>",
                hashed_password="$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW",  # "admin"
                full_name="系统管理员",
                is_active=True
            )
            db.add(default_user)

            # 创建示例公司
            sample_companies = [
                Company(
                    id="1",
                    name="阿里巴巴集团",
                    code="BABA",
                    industry="电商/云计算",
                    description="全球领先的电子商务和云计算公司",
                    address="浙江省杭州市余杭区文一西路969号",
                    contact_person="张三",
                    contact_phone="0571-85022088",
                    contact_email="<EMAIL>",
                    created_by="1",
                    is_active=True
                ),
                Company(
                    id="2",
                    name="腾讯控股",
                    code="TCEHY",
                    industry="互联网服务",
                    description="中国领先的互联网增值服务提供商",
                    address="广东省深圳市南山区科技中一路腾讯大厦",
                    contact_person="李四",
                    contact_phone="0755-86013388",
                    contact_email="<EMAIL>",
                    created_by="1",
                    is_active=True
                ),
                Company(
                    id="3",
                    name="比亚迪股份",
                    code="BYD",
                    industry="新能源汽车",
                    description="全球领先的新能源汽车制造商",
                    address="广东省深圳市坪山区比亚迪路3009号",
                    contact_person="王五",
                    contact_phone="0755-89888888",
                    contact_email="<EMAIL>",
                    created_by="1",
                    is_active=True
                )
            ]

            for company in sample_companies:
                db.add(company)

            db.commit()
            print("✅ 初始数据创建完成")
        else:
            print("✅ 数据库已存在数据，跳过初始化")

    except Exception as e:
        print(f"❌ 初始化数据失败: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    print("🚀 开始初始化数据库...")
    try:
        init_database()
        print("🎉 数据库初始化成功！")
        print("\n📋 默认用户信息:")
        print("   用户名: admin")
        print("   密码: admin")
        print("   邮箱: <EMAIL>")
        print("\n🏢 已创建示例公司:")
        print("   1. 阿里巴巴集团 (BABA)")
        print("   2. 腾讯控股 (TCEHY)")
        print("   3. 比亚迪股份 (BYD)")
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        sys.exit(1)
