"""
PDF报告生成器
使用ReportLab生成高质量的PDF报告，支持中文字体
"""

from typing import Dict, Any, List
from io import BytesIO
import os
from datetime import datetime
import logging

from reportlab.lib.pagesizes import A4, letter, landscape
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.colors import Color, HexColor, colors
from reportlab.lib.units import inch, cm, mm
from reportlab.platypus import (
    SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle,
    Image, PageBreak, KeepTogether, NextPageTemplate, PageTemplate
)
from reportlab.platypus.flowables import HRFlowable
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT, TA_JUSTIFY
from reportlab.graphics.shapes import Drawing, String, Line, Rect
from reportlab.graphics.charts.linecharts import HorizontalLineChart
from reportlab.graphics.charts.barcharts import VerticalBarChart
from reportlab.graphics.charts.piecharts import Pie
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.platypus.doctemplate import BaseDocTemplate
from reportlab.platypus.frames import Frame

from ..templates.base_template import TemplateConfig, ReportSection

logger = logging.getLogger(__name__)


class PDFGenerator:
    """专业财务报告PDF生成器"""

    def __init__(self, config: TemplateConfig):
        self.config = config
        self._register_fonts()
        self.styles = self._create_styles()

    def _register_fonts(self):
        """注册中文字体"""
        try:
            # 尝试注册系统中文字体
            font_paths = [
                '/System/Library/Fonts/PingFang.ttc',  # macOS
                '/System/Library/Fonts/Helvetica.ttc',  # macOS fallback
                'C:/Windows/Fonts/msyh.ttc',  # Windows 微软雅黑
                'C:/Windows/Fonts/simsun.ttc',  # Windows 宋体
                '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf',  # Linux
            ]

            font_registered = False
            for font_path in font_paths:
                if os.path.exists(font_path):
                    try:
                        if 'PingFang' in font_path:
                            pdfmetrics.registerFont(TTFont('ChineseFont', font_path, subfontIndex=0))
                            pdfmetrics.registerFont(TTFont('ChineseFont-Bold', font_path, subfontIndex=1))
                        elif 'msyh' in font_path:
                            pdfmetrics.registerFont(TTFont('ChineseFont', font_path, subfontIndex=0))
                            pdfmetrics.registerFont(TTFont('ChineseFont-Bold', font_path, subfontIndex=1))
                        elif 'simsun' in font_path:
                            pdfmetrics.registerFont(TTFont('ChineseFont', font_path))
                            pdfmetrics.registerFont(TTFont('ChineseFont-Bold', font_path))
                        else:
                            pdfmetrics.registerFont(TTFont('ChineseFont', font_path))
                            pdfmetrics.registerFont(TTFont('ChineseFont-Bold', font_path))
                        font_registered = True
                        logger.info(f"成功注册字体: {font_path}")
                        break
                    except Exception as e:
                        logger.warning(f"注册字体失败 {font_path}: {e}")
                        continue

            if not font_registered:
                logger.warning("未找到合适的中文字体，使用默认字体")
                # 使用默认字体
                self.chinese_font = 'Helvetica'
                self.chinese_font_bold = 'Helvetica-Bold'
            else:
                self.chinese_font = 'ChineseFont'
                self.chinese_font_bold = 'ChineseFont-Bold'

        except Exception as e:
            logger.error(f"字体注册过程出错: {e}")
            self.chinese_font = 'Helvetica'
            self.chinese_font_bold = 'Helvetica-Bold'

    def _create_styles(self) -> Dict[str, ParagraphStyle]:
        """创建专业财务报告样式"""
        styles = getSampleStyleSheet()
        custom_styles = {}

        # 报告标题样式
        custom_styles['ReportTitle'] = ParagraphStyle(
            'ReportTitle',
            parent=styles['Title'],
            fontSize=24,
            textColor=HexColor('#1a365d'),
            spaceAfter=30,
            spaceBefore=20,
            alignment=TA_CENTER,
            fontName=self.chinese_font_bold,
            leading=30
        )

        # 公司名称样式
        custom_styles['CompanyName'] = ParagraphStyle(
            'CompanyName',
            parent=styles['Normal'],
            fontSize=18,
            textColor=HexColor('#2d3748'),
            spaceAfter=15,
            alignment=TA_CENTER,
            fontName=self.chinese_font_bold
        )

        # 章节标题样式
        custom_styles['SectionTitle'] = ParagraphStyle(
            'SectionTitle',
            parent=styles['Heading1'],
            fontSize=16,
            textColor=HexColor('#1a365d'),
            spaceAfter=15,
            spaceBefore=25,
            fontName=self.chinese_font_bold,
            borderWidth=0,
            borderPadding=0,
            leftIndent=0,
            borderColor=HexColor('#e2e8f0')
        )

        # 子标题样式
        custom_styles['SubTitle'] = ParagraphStyle(
            'SubTitle',
            parent=styles['Heading2'],
            fontSize=14,
            textColor=HexColor('#2d3748'),
            spaceAfter=10,
            spaceBefore=15,
            fontName=self.chinese_font_bold
        )

        # 正文样式
        custom_styles['BodyText'] = ParagraphStyle(
            'BodyText',
            parent=styles['Normal'],
            fontSize=11,
            textColor=HexColor('#4a5568'),
            spaceAfter=8,
            alignment=TA_JUSTIFY,
            fontName=self.chinese_font,
            leading=16
        )

        # 重要信息样式
        custom_styles['ImportantText'] = ParagraphStyle(
            'ImportantText',
            parent=styles['Normal'],
            fontSize=12,
            textColor=HexColor('#c53030'),
            spaceAfter=8,
            fontName=self.chinese_font_bold
        )

        # 说明文字样式
        custom_styles['CaptionText'] = ParagraphStyle(
            'CaptionText',
            parent=styles['Normal'],
            fontSize=9,
            textColor=HexColor('#718096'),
            spaceAfter=4,
            alignment=TA_CENTER,
            fontName=self.chinese_font
        )

        # 表格标题样式
        custom_styles['TableTitle'] = ParagraphStyle(
            'TableTitle',
            parent=styles['Normal'],
            fontSize=12,
            textColor=HexColor('#2d3748'),
            spaceAfter=8,
            spaceBefore=10,
            fontName=self.chinese_font_bold
        )

        return custom_styles
    
    def generate_professional_report(self, data: Dict[str, Any]) -> bytes:
        """生成专业财务分析报告"""
        buffer = BytesIO()

        # 创建文档
        doc = SimpleDocTemplate(
            buffer,
            pagesize=A4,
            rightMargin=2*cm,
            leftMargin=2*cm,
            topMargin=2.5*cm,
            bottomMargin=2*cm,
            title="财务分析报告"
        )

        # 构建报告内容
        story = []

        # 1. 报告封面
        story.extend(self._create_cover_page(data))
        story.append(PageBreak())

        # 2. 执行摘要
        story.extend(self._create_executive_summary(data))
        story.append(PageBreak())

        # 3. 数据展示部分
        story.extend(self._create_data_section(data))
        story.append(PageBreak())

        # 4. 图形展示部分
        story.extend(self._create_charts_section(data))
        story.append(PageBreak())

        # 5. 名词解释部分
        story.extend(self._create_glossary_section())
        story.append(PageBreak())

        # 6. 报告结论部分
        story.extend(self._create_conclusion_section(data))

        # 生成PDF
        doc.build(story)
        buffer.seek(0)
        return buffer.getvalue()

    def generate_executive_summary(self, data: Dict[str, Any], sections: List[ReportSection]) -> bytes:
        """生成一页纸精华总结PDF（保持向后兼容）"""
        return self.generate_professional_report(data)
    
    def _create_header(self, data: Dict[str, Any]) -> List:
        """创建报告头部"""
        elements = []
        
        company_info = data['company_info']
        period_info = data['period_info']
        
        # 品牌Logo（如果有）
        if self.config.brand_logo and os.path.exists(self.config.brand_logo):
            logo = Image(self.config.brand_logo, width=2*inch, height=0.8*inch)
            logo.hAlign = 'LEFT'
            elements.append(logo)
        
        # 报告标题
        title = f"{company_info.get('name', '未知公司')} 财务分析报告"
        elements.append(Paragraph(title, self.styles['CustomTitle']))
        
        # 期间信息
        period_text = f"报告期间: {period_info.get('start_date', '')} 至 {period_info.get('end_date', '')}"
        elements.append(Paragraph(period_text, self.styles['CustomCaption']))
        
        # 生成时间
        generated_time = f"生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M')}"
        elements.append(Paragraph(generated_time, self.styles['CustomCaption']))
        
        elements.append(Spacer(1, 20))
        elements.append(HRFlowable(width="100%", thickness=1, color=HexColor(self.config.brand_colors['primary'])))
        elements.append(Spacer(1, 20))
        
        return elements
    
    def _create_metrics_grid(self, data: Dict[str, Any], section: ReportSection) -> List:
        """创建核心指标网格"""
        elements = []
        
        elements.append(Paragraph(section.title, self.styles['CustomHeading1']))
        
        core_metrics = data['core_metrics']
        metric_changes = data['metric_changes']
        
        # 创建指标表格
        table_data = []
        metrics_config = section.data['metrics']
        
        # 表头
        table_data.append(['指标', '当期值', '同比变化', '趋势'])
        
        for metric in metrics_config:
            key = metric['key']
            name = metric['name']
            format_type = metric['format']
            
            # 格式化数值
            value = core_metrics.get(key, 0)
            if format_type == 'currency':
                formatted_value = f"¥{value:,.0f}"
            elif format_type == 'percent':
                formatted_value = f"{value:.1f}%"
            else:
                formatted_value = f"{value:,.2f}"
            
            # 同比变化
            change = metric_changes.get(key, 0)
            change_text = f"{change:+.1f}%" if change != 0 else "0.0%"
            
            # 趋势箭头
            if change > 5:
                trend = "↗ 上升"
            elif change < -5:
                trend = "↘ 下降"
            else:
                trend = "→ 平稳"
            
            table_data.append([name, formatted_value, change_text, trend])
        
        # 创建表格
        table = Table(table_data, colWidths=[3*cm, 3*cm, 2.5*cm, 2.5*cm])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), HexColor(self.config.brand_colors['primary'])),
            ('TEXTCOLOR', (0, 0), (-1, 0), HexColor('#FFFFFF')),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('GRID', (0, 0), (-1, -1), 1, HexColor('#CCCCCC')),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ]))
        
        elements.append(table)
        elements.append(Spacer(1, 20))
        
        return elements
    
    def _create_chart_section(self, data: Dict[str, Any], section: ReportSection) -> List:
        """创建图表部分"""
        elements = []
        
        elements.append(Paragraph(section.title, self.styles['CustomHeading1']))
        
        # 生成图表
        chart_data = data.get('chart_data', {})
        chart_config = section.data
        
        if chart_config['chart_type'] == 'line':
            chart = self._create_line_chart(chart_data, chart_config)
        elif chart_config['chart_type'] == 'bar':
            chart = self._create_bar_chart(chart_data, chart_config)
        else:
            chart = None
        
        if chart:
            elements.append(chart)
        else:
            elements.append(Paragraph("图表数据不可用", self.styles['CustomCaption']))
        
        elements.append(Spacer(1, 20))
        
        return elements
    
    def _create_score_card(self, data: Dict[str, Any], section: ReportSection) -> List:
        """创建评分卡"""
        elements = []
        
        elements.append(Paragraph(section.title, self.styles['CustomHeading1']))
        
        health_score = data['health_score']
        
        # 总分显示
        total_score = health_score['total']
        level = health_score['level']
        
        score_text = f"<b>综合评分: {total_score}/100 ({level})</b>"
        elements.append(Paragraph(score_text, self.styles['CustomBody']))
        
        # 分类评分表格
        categories = health_score['categories']
        table_data = [['评估维度', '得分', '等级']]
        
        category_names = {
            'profitability': '盈利能力',
            'solvency': '偿债能力',
            'operational': '运营能力',
            'growth': '成长能力'
        }
        
        for key, score in categories.items():
            name = category_names.get(key, key)
            level = "优秀" if score >= 80 else "良好" if score >= 60 else "一般" if score >= 40 else "较差"
            table_data.append([name, f"{score:.1f}", level])
        
        table = Table(table_data, colWidths=[4*cm, 2*cm, 2*cm])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), HexColor(self.config.brand_colors['secondary'])),
            ('TEXTCOLOR', (0, 0), (-1, 0), HexColor('#FFFFFF')),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 11),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, HexColor('#CCCCCC')),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ]))
        
        elements.append(table)
        elements.append(Spacer(1, 20))
        
        return elements
    
    def _create_alert_list(self, data: Dict[str, Any], section: ReportSection) -> List:
        """创建风险预警列表"""
        elements = []
        
        elements.append(Paragraph(section.title, self.styles['CustomHeading1']))
        
        risk_alerts = data['risk_alerts']
        
        if not risk_alerts:
            elements.append(Paragraph("暂无重大风险预警", self.styles['CustomBody']))
        else:
            for alert in risk_alerts:
                # 风险类型和等级
                level_color = "#ff4d4f" if alert['level'] == 'high' else "#faad14"
                alert_text = f"<b><font color='{level_color}'>⚠ {alert['type']}</font></b><br/>"
                alert_text += f"{alert['message']}<br/>"
                alert_text += f"<i>建议: {alert['suggestion']}</i>"
                
                elements.append(Paragraph(alert_text, self.styles['CustomBody']))
                elements.append(Spacer(1, 8))
        
        elements.append(Spacer(1, 12))
        
        return elements
    
    def _create_recommendation_list(self, data: Dict[str, Any], section: ReportSection) -> List:
        """创建建议列表"""
        elements = []
        
        elements.append(Paragraph(section.title, self.styles['CustomHeading1']))
        
        recommendations = data['recommendations']
        
        if not recommendations:
            elements.append(Paragraph("暂无特别建议", self.styles['CustomBody']))
        else:
            for i, rec in enumerate(recommendations, 1):
                priority_color = "#52c41a" if rec['priority'] == 'high' else "#1890ff"
                rec_text = f"<b>{i}. {rec['title']}</b><br/>"
                rec_text += f"<font color='{priority_color}'>优先级: {rec['priority'].upper()}</font><br/>"
                rec_text += f"{rec['description']}"
                
                elements.append(Paragraph(rec_text, self.styles['CustomBody']))
                elements.append(Spacer(1, 8))
        
        return elements
    
    def _create_line_chart(self, chart_data: Dict[str, Any], config: Dict[str, Any]) -> Drawing:
        """创建折线图"""
        # 这里应该使用实际的图表数据生成折线图
        # 由于ReportLab的图表功能相对简单，这里返回一个占位符
        drawing = Drawing(400, 200)
        
        # 添加图表标题
        from reportlab.graphics.shapes import String
        drawing.add(String(200, 180, "收入与利润趋势图", textAnchor='middle'))
        
        return drawing
    
    def _create_bar_chart(self, chart_data: Dict[str, Any], config: Dict[str, Any]) -> Drawing:
        """创建柱状图"""
        drawing = Drawing(400, 200)
        
        from reportlab.graphics.shapes import String
        drawing.add(String(200, 180, "财务指标对比图", textAnchor='middle'))
        
        return drawing

    def _create_cover_page(self, data: Dict[str, Any]) -> List:
        """创建报告封面"""
        elements = []

        company_info = data.get('company_info', {})
        period_info = data.get('period_info', {})

        # 添加空白间距
        elements.append(Spacer(1, 3*cm))

        # 报告标题
        title = "财务分析报告"
        elements.append(Paragraph(title, self.styles['ReportTitle']))
        elements.append(Spacer(1, 1*cm))

        # 公司名称
        company_name = company_info.get('name', '示例科技有限公司')
        elements.append(Paragraph(company_name, self.styles['CompanyName']))
        elements.append(Spacer(1, 2*cm))

        # 报告期间信息表格
        period_data = [
            ['报告期间', f"{period_info.get('start_date', '2024-01-01')} 至 {period_info.get('end_date', '2024-03-31')}"],
            ['报告类型', '季度财务分析报告'],
            ['生成时间', datetime.now().strftime('%Y年%m月%d日')],
            ['报告版本', 'V1.0']
        ]

        period_table = Table(period_data, colWidths=[4*cm, 8*cm])
        period_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), HexColor('#f7fafc')),
            ('TEXTCOLOR', (0, 0), (-1, -1), HexColor('#2d3748')),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), self.chinese_font_bold),
            ('FONTNAME', (1, 0), (1, -1), self.chinese_font),
            ('FONTSIZE', (0, 0), (-1, -1), 11),
            ('GRID', (0, 0), (-1, -1), 1, HexColor('#e2e8f0')),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('LEFTPADDING', (0, 0), (-1, -1), 12),
            ('RIGHTPADDING', (0, 0), (-1, -1), 12),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
        ]))

        elements.append(period_table)
        elements.append(Spacer(1, 3*cm))

        # 免责声明
        disclaimer = """
        <b>重要声明：</b><br/>
        本报告基于公司提供的财务数据进行分析，仅供内部管理决策参考。
        报告中的分析结论和建议不构成投资建议，请结合实际情况谨慎使用。
        """
        elements.append(Paragraph(disclaimer, self.styles['CaptionText']))

        return elements

    def _create_executive_summary(self, data: Dict[str, Any]) -> List:
        """创建执行摘要"""
        elements = []

        # 章节标题
        elements.append(Paragraph("执行摘要", self.styles['SectionTitle']))
        elements.append(Spacer(1, 0.5*cm))

        # 核心指标概览
        elements.append(Paragraph("核心财务指标", self.styles['SubTitle']))

        # 模拟核心指标数据
        metrics_data = data.get('core_metrics', {
            'revenue': {'value': 12450000, 'change': 12.5, 'name': '营业收入'},
            'net_profit': {'value': 2340000, 'change': 8.3, 'name': '净利润'},
            'gross_margin': {'value': 45.2, 'change': -2.1, 'name': '毛利率'},
            'roe': {'value': 15.8, 'change': 1.2, 'name': '净资产收益率'}
        })

        # 创建指标表格
        metrics_table_data = [['指标名称', '当期数值', '同比变化', '趋势分析']]

        for key, metric in metrics_data.items():
            value = metric['value']
            change = metric['change']
            name = metric['name']

            # 格式化数值
            if key in ['revenue', 'net_profit']:
                formatted_value = f"¥{value:,.0f}"
            else:
                formatted_value = f"{value:.1f}%"

            # 变化趋势
            change_text = f"{change:+.1f}%"
            trend = "↗ 上升" if change > 0 else "↘ 下降" if change < 0 else "→ 平稳"

            metrics_table_data.append([name, formatted_value, change_text, trend])

        metrics_table = Table(metrics_table_data, colWidths=[4*cm, 3*cm, 2.5*cm, 2.5*cm])
        metrics_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), HexColor('#4a90e2')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), self.chinese_font_bold),
            ('FONTNAME', (0, 1), (-1, -1), self.chinese_font),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, HexColor('#e2e8f0')),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, HexColor('#f8f9fa')]),
        ]))

        elements.append(metrics_table)
        elements.append(Spacer(1, 0.5*cm))

        # 关键发现
        elements.append(Paragraph("关键发现", self.styles['SubTitle']))

        findings = [
            "营业收入同比增长12.5%，显示良好的业务增长态势",
            "净利润增长8.3%，盈利能力保持稳定",
            "毛利率下降2.1%，需要关注成本控制",
            "净资产收益率提升1.2%，资产运营效率改善"
        ]

        for i, finding in enumerate(findings, 1):
            finding_text = f"{i}. {finding}"
            elements.append(Paragraph(finding_text, self.styles['BodyText']))

        elements.append(Spacer(1, 0.5*cm))

        # 主要风险提示
        elements.append(Paragraph("主要风险提示", self.styles['SubTitle']))

        risks = [
            "现金流状况需要密切关注，确保流动性充足",
            "应收账款周转率下降，需要加强应收账款管理",
            "原材料成本上涨压力，影响毛利率表现"
        ]

        for i, risk in enumerate(risks, 1):
            risk_text = f"<font color='#c53030'>{i}. {risk}</font>"
            elements.append(Paragraph(risk_text, self.styles['BodyText']))

        return elements

    def _create_data_section(self, data: Dict[str, Any]) -> List:
        """创建数据展示部分"""
        elements = []

        # 章节标题
        elements.append(Paragraph("财务数据分析", self.styles['SectionTitle']))
        elements.append(Spacer(1, 0.5*cm))

        # 1. 利润表分析
        elements.append(Paragraph("1. 利润表分析", self.styles['SubTitle']))

        # 利润表数据
        income_data = [
            ['项目', '本期金额(万元)', '上期金额(万元)', '变动金额', '变动比例'],
            ['营业收入', '1,245.00', '1,106.67', '+138.33', '+12.5%'],
            ['营业成本', '682.75', '608.67', '+74.08', '+12.2%'],
            ['毛利润', '562.25', '498.00', '+64.25', '+12.9%'],
            ['销售费用', '124.50', '110.67', '+13.83', '+12.5%'],
            ['管理费用', '87.15', '77.47', '+9.68', '+12.5%'],
            ['财务费用', '12.45', '11.07', '+1.38', '+12.5%'],
            ['净利润', '234.00', '215.83', '+18.17', '+8.4%']
        ]

        income_table = Table(income_data, colWidths=[3*cm, 2.5*cm, 2.5*cm, 2*cm, 2*cm])
        income_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), HexColor('#4a90e2')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('ALIGN', (0, 0), (0, -1), 'LEFT'),
            ('ALIGN', (1, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), self.chinese_font_bold),
            ('FONTNAME', (0, 1), (-1, -1), self.chinese_font),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('GRID', (0, 0), (-1, -1), 1, HexColor('#e2e8f0')),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, HexColor('#f8f9fa')]),
        ]))

        elements.append(income_table)
        elements.append(Spacer(1, 0.3*cm))

        # 利润表分析说明
        income_analysis = """
        <b>分析要点：</b><br/>
        • 营业收入同比增长12.5%，显示业务规模持续扩张<br/>
        • 毛利率为45.2%，较上期略有提升，盈利质量良好<br/>
        • 期间费用控制合理，费用率保持稳定<br/>
        • 净利润增长8.4%，盈利能力稳步提升
        """
        elements.append(Paragraph(income_analysis, self.styles['BodyText']))
        elements.append(Spacer(1, 0.5*cm))

        # 2. 资产负债表分析
        elements.append(Paragraph("2. 资产负债表分析", self.styles['SubTitle']))

        # 资产负债表数据
        balance_data = [
            ['项目', '期末余额(万元)', '期初余额(万元)', '变动金额', '变动比例'],
            ['流动资产', '2,450.00', '2,180.00', '+270.00', '+12.4%'],
            ['非流动资产', '1,850.00', '1,720.00', '+130.00', '+7.6%'],
            ['资产总计', '4,300.00', '3,900.00', '+400.00', '+10.3%'],
            ['流动负债', '1,200.00', '1,100.00', '+100.00', '+9.1%'],
            ['非流动负债', '800.00', '750.00', '+50.00', '+6.7%'],
            ['负债合计', '2,000.00', '1,850.00', '+150.00', '+8.1%'],
            ['所有者权益', '2,300.00', '2,050.00', '+250.00', '+12.2%']
        ]

        balance_table = Table(balance_data, colWidths=[3*cm, 2.5*cm, 2.5*cm, 2*cm, 2*cm])
        balance_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), HexColor('#52c41a')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('ALIGN', (0, 0), (0, -1), 'LEFT'),
            ('ALIGN', (1, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), self.chinese_font_bold),
            ('FONTNAME', (0, 1), (-1, -1), self.chinese_font),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('GRID', (0, 0), (-1, -1), 1, HexColor('#e2e8f0')),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, HexColor('#f8f9fa')]),
        ]))

        elements.append(balance_table)
        elements.append(Spacer(1, 0.3*cm))

        # 资产负债表分析说明
        balance_analysis = """
        <b>分析要点：</b><br/>
        • 资产总额增长10.3%，资产规模稳步扩张<br/>
        • 资产负债率为46.5%，财务结构合理<br/>
        • 流动比率为2.04，短期偿债能力良好<br/>
        • 所有者权益增长12.2%，资本实力增强
        """
        elements.append(Paragraph(balance_analysis, self.styles['BodyText']))

        return elements

    def _create_charts_section(self, data: Dict[str, Any]) -> List:
        """创建图形展示部分"""
        elements = []

        # 章节标题
        elements.append(Paragraph("图形分析", self.styles['SectionTitle']))
        elements.append(Spacer(1, 0.5*cm))

        # 1. 收入趋势图
        elements.append(Paragraph("1. 营业收入趋势分析", self.styles['SubTitle']))

        # 创建简单的图表占位符
        chart_placeholder = Drawing(12*cm, 6*cm)
        chart_placeholder.add(Rect(0, 0, 12*cm, 6*cm, fillColor=HexColor('#f8f9fa'), strokeColor=HexColor('#e2e8f0')))
        chart_placeholder.add(String(6*cm, 3*cm, "营业收入趋势图", textAnchor='middle', fontSize=14, fontName=self.chinese_font))
        chart_placeholder.add(String(6*cm, 2.5*cm, "（此处应显示实际图表）", textAnchor='middle', fontSize=10, fontName=self.chinese_font))

        elements.append(chart_placeholder)
        elements.append(Spacer(1, 0.3*cm))

        chart_desc = """
        <b>图表说明：</b>从趋势图可以看出，公司营业收入呈现稳步上升趋势，
        季度环比增长率保持在8%-15%之间，显示出良好的业务增长动能。
        """
        elements.append(Paragraph(chart_desc, self.styles['BodyText']))
        elements.append(Spacer(1, 0.5*cm))

        # 2. 财务指标雷达图
        elements.append(Paragraph("2. 财务指标综合评价", self.styles['SubTitle']))

        radar_placeholder = Drawing(12*cm, 6*cm)
        radar_placeholder.add(Rect(0, 0, 12*cm, 6*cm, fillColor=HexColor('#f8f9fa'), strokeColor=HexColor('#e2e8f0')))
        radar_placeholder.add(String(6*cm, 3*cm, "财务指标雷达图", textAnchor='middle', fontSize=14, fontName=self.chinese_font))
        radar_placeholder.add(String(6*cm, 2.5*cm, "（盈利能力、偿债能力、运营能力、成长能力）", textAnchor='middle', fontSize=10, fontName=self.chinese_font))

        elements.append(radar_placeholder)
        elements.append(Spacer(1, 0.3*cm))

        radar_desc = """
        <b>综合评价：</b>公司在盈利能力和成长能力方面表现突出，
        偿债能力保持稳健水平，运营能力有待进一步提升。
        """
        elements.append(Paragraph(radar_desc, self.styles['BodyText']))

        return elements

    def _create_glossary_section(self) -> List:
        """创建名词解释部分"""
        elements = []

        # 章节标题
        elements.append(Paragraph("名词解释", self.styles['SectionTitle']))
        elements.append(Spacer(1, 0.5*cm))

        # 财务术语解释
        glossary_items = [
            {
                'term': '营业收入',
                'definition': '企业在日常活动中形成的、会导致所有者权益增加的、与所有者投入资本无关的经济利益的总流入。'
            },
            {
                'term': '毛利率',
                'definition': '毛利润占营业收入的百分比，反映企业产品或服务的盈利能力。计算公式：(营业收入-营业成本)/营业收入×100%。'
            },
            {
                'term': '净资产收益率(ROE)',
                'definition': '净利润与平均净资产的比率，衡量公司运用自有资本的效率。计算公式：净利润/平均净资产×100%。'
            },
            {
                'term': '流动比率',
                'definition': '流动资产与流动负债的比率，反映企业短期偿债能力。一般认为2:1的比率比较合适。'
            },
            {
                'term': '资产负债率',
                'definition': '负债总额与资产总额的比率，反映企业的负债水平和财务风险。计算公式：负债总额/资产总额×100%。'
            },
            {
                'term': '应收账款周转率',
                'definition': '营业收入与平均应收账款余额的比率，反映应收账款的收回速度。比率越高，说明收账效率越高。'
            },
            {
                'term': '现金流量',
                'definition': '企业在一定期间内现金和现金等价物的流入和流出量。分为经营活动、投资活动和筹资活动现金流量。'
            },
            {
                'term': 'EBITDA',
                'definition': '息税折旧摊销前利润，反映企业核心业务的盈利能力。计算公式：净利润+利息+税收+折旧+摊销。'
            }
        ]

        for item in glossary_items:
            term_text = f"<b>{item['term']}</b>"
            elements.append(Paragraph(term_text, self.styles['SubTitle']))
            elements.append(Paragraph(item['definition'], self.styles['BodyText']))
            elements.append(Spacer(1, 0.2*cm))

        return elements

    def _create_conclusion_section(self, data: Dict[str, Any]) -> List:
        """创建报告结论部分"""
        elements = []

        # 章节标题
        elements.append(Paragraph("报告结论与建议", self.styles['SectionTitle']))
        elements.append(Spacer(1, 0.5*cm))

        # 1. 总体评价
        elements.append(Paragraph("1. 总体财务状况评价", self.styles['SubTitle']))

        overall_assessment = """
        基于本期财务数据分析，公司整体财务状况良好，具体表现如下：<br/><br/>

        <b>优势方面：</b><br/>
        • 营业收入保持稳定增长，业务发展态势良好<br/>
        • 盈利能力稳定，净利润实现正增长<br/>
        • 资产规模持续扩张，资本实力不断增强<br/>
        • 财务结构合理，偿债能力保持稳健<br/><br/>

        <b>关注要点：</b><br/>
        • 毛利率出现小幅下降，需要关注成本控制<br/>
        • 应收账款管理需要进一步加强<br/>
        • 现金流管理需要持续优化
        """
        elements.append(Paragraph(overall_assessment, self.styles['BodyText']))
        elements.append(Spacer(1, 0.5*cm))

        # 2. 风险提示
        elements.append(Paragraph("2. 主要风险提示", self.styles['SubTitle']))

        risk_warnings = [
            {
                'level': '中等风险',
                'item': '成本上涨压力',
                'description': '原材料价格上涨可能继续压缩毛利空间，需要通过提升产品附加值或优化供应链来应对。'
            },
            {
                'level': '低风险',
                'item': '应收账款风险',
                'description': '应收账款周转率略有下降，需要加强客户信用管理和催收工作。'
            },
            {
                'level': '低风险',
                'item': '流动性风险',
                'description': '虽然流动比率良好，但需要持续关注现金流状况，确保资金链安全。'
            }
        ]

        for risk in risk_warnings:
            level_color = '#faad14' if risk['level'] == '中等风险' else '#52c41a'
            risk_text = f"""
            <b><font color='{level_color}'>[{risk['level']}]</font> {risk['item']}</b><br/>
            {risk['description']}
            """
            elements.append(Paragraph(risk_text, self.styles['BodyText']))
            elements.append(Spacer(1, 0.2*cm))

        elements.append(Spacer(1, 0.3*cm))

        # 3. 管理建议
        elements.append(Paragraph("3. 管理建议", self.styles['SubTitle']))

        recommendations = [
            {
                'priority': '高',
                'category': '成本管理',
                'suggestion': '建立成本预警机制，加强供应商管理，探索成本优化空间，提升产品毛利率。'
            },
            {
                'priority': '高',
                'category': '现金流管理',
                'suggestion': '制定现金流预测计划，优化收付款周期，确保经营活动现金流为正。'
            },
            {
                'priority': '中',
                'category': '应收账款管理',
                'suggestion': '建立客户信用评级体系，制定差异化信用政策，加强应收账款催收。'
            },
            {
                'priority': '中',
                'category': '业务发展',
                'suggestion': '在保持现有业务稳定增长的基础上，积极拓展新的盈利增长点。'
            }
        ]

        for i, rec in enumerate(recommendations, 1):
            priority_color = '#c53030' if rec['priority'] == '高' else '#faad14'
            rec_text = f"""
            <b>{i}. {rec['category']}</b> <font color='{priority_color}'>[{rec['priority']}优先级]</font><br/>
            {rec['suggestion']}
            """
            elements.append(Paragraph(rec_text, self.styles['BodyText']))
            elements.append(Spacer(1, 0.3*cm))

        # 4. 报告说明
        elements.append(Spacer(1, 0.5*cm))
        elements.append(Paragraph("4. 报告说明", self.styles['SubTitle']))

        report_notes = """
        <b>数据来源：</b>本报告基于公司提供的财务报表数据进行分析。<br/>
        <b>分析方法：</b>采用财务比率分析、趋势分析、同比分析等方法。<br/>
        <b>报告用途：</b>本报告仅供内部管理决策参考，不构成投资建议。<br/>
        <b>更新频率：</b>建议每季度更新一次，以跟踪财务状况变化。<br/>
        <b>联系方式：</b>如有疑问，请联系财务分析团队。
        """
        elements.append(Paragraph(report_notes, self.styles['CaptionText']))

        return elements
