"""
PDF报告生成器
使用ReportLab生成高质量的PDF报告，支持中文字体
"""

from typing import Dict, Any, List
from io import BytesIO
import os
from datetime import datetime
import logging

from reportlab.lib.pagesizes import A4, letter, landscape
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.colors import Color, HexColor, colors
from reportlab.lib.units import inch, cm, mm
from reportlab.platypus import (
    SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle,
    Image, PageBreak, KeepTogether, NextPageTemplate, PageTemplate
)
from reportlab.platypus.flowables import HRFlowable
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT, TA_JUSTIFY
from reportlab.graphics.shapes import Drawing, String, Line, Rect
from reportlab.graphics.charts.linecharts import HorizontalLineChart
from reportlab.graphics.charts.barcharts import VerticalBarChart
from reportlab.graphics.charts.piecharts import Pie
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.platypus.doctemplate import BaseDocTemplate
from reportlab.platypus.frames import Frame

from ..templates.base_template import TemplateConfig, ReportSection

logger = logging.getLogger(__name__)


class PDFGenerator:
    """专业财务报告PDF生成器"""

    def __init__(self, config: TemplateConfig):
        self.config = config
        self._register_fonts()
        self.styles = self._create_styles()

    def _register_fonts(self):
        """注册中文字体"""
        try:
            # 尝试注册系统中文字体
            font_paths = [
                '/System/Library/Fonts/PingFang.ttc',  # macOS
                '/System/Library/Fonts/Helvetica.ttc',  # macOS fallback
                'C:/Windows/Fonts/msyh.ttc',  # Windows 微软雅黑
                'C:/Windows/Fonts/simsun.ttc',  # Windows 宋体
                '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf',  # Linux
            ]

            font_registered = False
            for font_path in font_paths:
                if os.path.exists(font_path):
                    try:
                        if 'PingFang' in font_path:
                            pdfmetrics.registerFont(TTFont('ChineseFont', font_path, subfontIndex=0))
                            pdfmetrics.registerFont(TTFont('ChineseFont-Bold', font_path, subfontIndex=1))
                        elif 'msyh' in font_path:
                            pdfmetrics.registerFont(TTFont('ChineseFont', font_path, subfontIndex=0))
                            pdfmetrics.registerFont(TTFont('ChineseFont-Bold', font_path, subfontIndex=1))
                        elif 'simsun' in font_path:
                            pdfmetrics.registerFont(TTFont('ChineseFont', font_path))
                            pdfmetrics.registerFont(TTFont('ChineseFont-Bold', font_path))
                        else:
                            pdfmetrics.registerFont(TTFont('ChineseFont', font_path))
                            pdfmetrics.registerFont(TTFont('ChineseFont-Bold', font_path))
                        font_registered = True
                        logger.info(f"成功注册字体: {font_path}")
                        break
                    except Exception as e:
                        logger.warning(f"注册字体失败 {font_path}: {e}")
                        continue

            if not font_registered:
                logger.warning("未找到合适的中文字体，使用默认字体")
                # 使用默认字体
                self.chinese_font = 'Helvetica'
                self.chinese_font_bold = 'Helvetica-Bold'
            else:
                self.chinese_font = 'ChineseFont'
                self.chinese_font_bold = 'ChineseFont-Bold'

        except Exception as e:
            logger.error(f"字体注册过程出错: {e}")
            self.chinese_font = 'Helvetica'
            self.chinese_font_bold = 'Helvetica-Bold'

    def _create_styles(self) -> Dict[str, ParagraphStyle]:
        """创建专业财务报告样式"""
        styles = getSampleStyleSheet()
        custom_styles = {}

        # 报告标题样式
        custom_styles['ReportTitle'] = ParagraphStyle(
            'ReportTitle',
            parent=styles['Title'],
            fontSize=24,
            textColor=HexColor('#1a365d'),
            spaceAfter=30,
            spaceBefore=20,
            alignment=TA_CENTER,
            fontName=self.chinese_font_bold,
            leading=30
        )

        # 公司名称样式
        custom_styles['CompanyName'] = ParagraphStyle(
            'CompanyName',
            parent=styles['Normal'],
            fontSize=18,
            textColor=HexColor('#2d3748'),
            spaceAfter=15,
            alignment=TA_CENTER,
            fontName=self.chinese_font_bold
        )

        # 章节标题样式
        custom_styles['SectionTitle'] = ParagraphStyle(
            'SectionTitle',
            parent=styles['Heading1'],
            fontSize=16,
            textColor=HexColor('#1a365d'),
            spaceAfter=15,
            spaceBefore=25,
            fontName=self.chinese_font_bold,
            borderWidth=0,
            borderPadding=0,
            leftIndent=0,
            borderColor=HexColor('#e2e8f0')
        )

        # 子标题样式
        custom_styles['SubTitle'] = ParagraphStyle(
            'SubTitle',
            parent=styles['Heading2'],
            fontSize=14,
            textColor=HexColor('#2d3748'),
            spaceAfter=10,
            spaceBefore=15,
            fontName=self.chinese_font_bold
        )

        # 正文样式
        custom_styles['BodyText'] = ParagraphStyle(
            'BodyText',
            parent=styles['Normal'],
            fontSize=11,
            textColor=HexColor('#4a5568'),
            spaceAfter=8,
            alignment=TA_JUSTIFY,
            fontName=self.chinese_font,
            leading=16
        )

        # 重要信息样式
        custom_styles['ImportantText'] = ParagraphStyle(
            'ImportantText',
            parent=styles['Normal'],
            fontSize=12,
            textColor=HexColor('#c53030'),
            spaceAfter=8,
            fontName=self.chinese_font_bold
        )

        # 说明文字样式
        custom_styles['CaptionText'] = ParagraphStyle(
            'CaptionText',
            parent=styles['Normal'],
            fontSize=9,
            textColor=HexColor('#718096'),
            spaceAfter=4,
            alignment=TA_CENTER,
            fontName=self.chinese_font
        )

        # 表格标题样式
        custom_styles['TableTitle'] = ParagraphStyle(
            'TableTitle',
            parent=styles['Normal'],
            fontSize=12,
            textColor=HexColor('#2d3748'),
            spaceAfter=8,
            spaceBefore=10,
            fontName=self.chinese_font_bold
        )

        return custom_styles
    
    def generate_professional_report(self, data: Dict[str, Any]) -> bytes:
        """生成专业财务分析报告"""
        buffer = BytesIO()

        # 创建文档
        doc = SimpleDocTemplate(
            buffer,
            pagesize=A4,
            rightMargin=2*cm,
            leftMargin=2*cm,
            topMargin=2.5*cm,
            bottomMargin=2*cm,
            title="财务分析报告"
        )

        # 构建报告内容
        story = []

        # 1. 报告封面
        story.extend(self._create_cover_page(data))
        story.append(PageBreak())

        # 2. 执行摘要
        story.extend(self._create_executive_summary(data))
        story.append(PageBreak())

        # 3. 数据展示部分
        story.extend(self._create_data_section(data))
        story.append(PageBreak())

        # 4. 图形展示部分
        story.extend(self._create_charts_section(data))
        story.append(PageBreak())

        # 5. 名词解释部分
        story.extend(self._create_glossary_section())
        story.append(PageBreak())

        # 6. 报告结论部分
        story.extend(self._create_conclusion_section(data))

        # 生成PDF
        doc.build(story)
        buffer.seek(0)
        return buffer.getvalue()

    def generate_executive_summary(self, data: Dict[str, Any], sections: List[ReportSection]) -> bytes:
        """生成一页纸精华总结PDF（保持向后兼容）"""
        return self.generate_professional_report(data)
    
    def _create_header(self, data: Dict[str, Any]) -> List:
        """创建报告头部"""
        elements = []
        
        company_info = data['company_info']
        period_info = data['period_info']
        
        # 品牌Logo（如果有）
        if self.config.brand_logo and os.path.exists(self.config.brand_logo):
            logo = Image(self.config.brand_logo, width=2*inch, height=0.8*inch)
            logo.hAlign = 'LEFT'
            elements.append(logo)
        
        # 报告标题
        title = f"{company_info.get('name', '未知公司')} 财务分析报告"
        elements.append(Paragraph(title, self.styles['CustomTitle']))
        
        # 期间信息
        period_text = f"报告期间: {period_info.get('start_date', '')} 至 {period_info.get('end_date', '')}"
        elements.append(Paragraph(period_text, self.styles['CustomCaption']))
        
        # 生成时间
        generated_time = f"生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M')}"
        elements.append(Paragraph(generated_time, self.styles['CustomCaption']))
        
        elements.append(Spacer(1, 20))
        elements.append(HRFlowable(width="100%", thickness=1, color=HexColor(self.config.brand_colors['primary'])))
        elements.append(Spacer(1, 20))
        
        return elements
    
    def _create_metrics_grid(self, data: Dict[str, Any], section: ReportSection) -> List:
        """创建核心指标网格"""
        elements = []
        
        elements.append(Paragraph(section.title, self.styles['CustomHeading1']))
        
        core_metrics = data['core_metrics']
        metric_changes = data['metric_changes']
        
        # 创建指标表格
        table_data = []
        metrics_config = section.data['metrics']
        
        # 表头
        table_data.append(['指标', '当期值', '同比变化', '趋势'])
        
        for metric in metrics_config:
            key = metric['key']
            name = metric['name']
            format_type = metric['format']
            
            # 格式化数值
            value = core_metrics.get(key, 0)
            if format_type == 'currency':
                formatted_value = f"¥{value:,.0f}"
            elif format_type == 'percent':
                formatted_value = f"{value:.1f}%"
            else:
                formatted_value = f"{value:,.2f}"
            
            # 同比变化
            change = metric_changes.get(key, 0)
            change_text = f"{change:+.1f}%" if change != 0 else "0.0%"
            
            # 趋势箭头
            if change > 5:
                trend = "↗ 上升"
            elif change < -5:
                trend = "↘ 下降"
            else:
                trend = "→ 平稳"
            
            table_data.append([name, formatted_value, change_text, trend])
        
        # 创建表格
        table = Table(table_data, colWidths=[3*cm, 3*cm, 2.5*cm, 2.5*cm])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), HexColor(self.config.brand_colors['primary'])),
            ('TEXTCOLOR', (0, 0), (-1, 0), HexColor('#FFFFFF')),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('GRID', (0, 0), (-1, -1), 1, HexColor('#CCCCCC')),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ]))
        
        elements.append(table)
        elements.append(Spacer(1, 20))
        
        return elements
    
    def _create_chart_section(self, data: Dict[str, Any], section: ReportSection) -> List:
        """创建图表部分"""
        elements = []
        
        elements.append(Paragraph(section.title, self.styles['CustomHeading1']))
        
        # 生成图表
        chart_data = data.get('chart_data', {})
        chart_config = section.data
        
        if chart_config['chart_type'] == 'line':
            chart = self._create_line_chart(chart_data, chart_config)
        elif chart_config['chart_type'] == 'bar':
            chart = self._create_bar_chart(chart_data, chart_config)
        else:
            chart = None
        
        if chart:
            elements.append(chart)
        else:
            elements.append(Paragraph("图表数据不可用", self.styles['CustomCaption']))
        
        elements.append(Spacer(1, 20))
        
        return elements
    
    def _create_score_card(self, data: Dict[str, Any], section: ReportSection) -> List:
        """创建评分卡"""
        elements = []
        
        elements.append(Paragraph(section.title, self.styles['CustomHeading1']))
        
        health_score = data['health_score']
        
        # 总分显示
        total_score = health_score['total']
        level = health_score['level']
        
        score_text = f"<b>综合评分: {total_score}/100 ({level})</b>"
        elements.append(Paragraph(score_text, self.styles['CustomBody']))
        
        # 分类评分表格
        categories = health_score['categories']
        table_data = [['评估维度', '得分', '等级']]
        
        category_names = {
            'profitability': '盈利能力',
            'solvency': '偿债能力',
            'operational': '运营能力',
            'growth': '成长能力'
        }
        
        for key, score in categories.items():
            name = category_names.get(key, key)
            level = "优秀" if score >= 80 else "良好" if score >= 60 else "一般" if score >= 40 else "较差"
            table_data.append([name, f"{score:.1f}", level])
        
        table = Table(table_data, colWidths=[4*cm, 2*cm, 2*cm])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), HexColor(self.config.brand_colors['secondary'])),
            ('TEXTCOLOR', (0, 0), (-1, 0), HexColor('#FFFFFF')),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 11),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, HexColor('#CCCCCC')),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ]))
        
        elements.append(table)
        elements.append(Spacer(1, 20))
        
        return elements
    
    def _create_alert_list(self, data: Dict[str, Any], section: ReportSection) -> List:
        """创建风险预警列表"""
        elements = []
        
        elements.append(Paragraph(section.title, self.styles['CustomHeading1']))
        
        risk_alerts = data['risk_alerts']
        
        if not risk_alerts:
            elements.append(Paragraph("暂无重大风险预警", self.styles['CustomBody']))
        else:
            for alert in risk_alerts:
                # 风险类型和等级
                level_color = "#ff4d4f" if alert['level'] == 'high' else "#faad14"
                alert_text = f"<b><font color='{level_color}'>⚠ {alert['type']}</font></b><br/>"
                alert_text += f"{alert['message']}<br/>"
                alert_text += f"<i>建议: {alert['suggestion']}</i>"
                
                elements.append(Paragraph(alert_text, self.styles['CustomBody']))
                elements.append(Spacer(1, 8))
        
        elements.append(Spacer(1, 12))
        
        return elements
    
    def _create_recommendation_list(self, data: Dict[str, Any], section: ReportSection) -> List:
        """创建建议列表"""
        elements = []
        
        elements.append(Paragraph(section.title, self.styles['CustomHeading1']))
        
        recommendations = data['recommendations']
        
        if not recommendations:
            elements.append(Paragraph("暂无特别建议", self.styles['CustomBody']))
        else:
            for i, rec in enumerate(recommendations, 1):
                priority_color = "#52c41a" if rec['priority'] == 'high' else "#1890ff"
                rec_text = f"<b>{i}. {rec['title']}</b><br/>"
                rec_text += f"<font color='{priority_color}'>优先级: {rec['priority'].upper()}</font><br/>"
                rec_text += f"{rec['description']}"
                
                elements.append(Paragraph(rec_text, self.styles['CustomBody']))
                elements.append(Spacer(1, 8))
        
        return elements
    
    def _create_line_chart(self, chart_data: Dict[str, Any], config: Dict[str, Any]) -> Drawing:
        """创建折线图"""
        # 这里应该使用实际的图表数据生成折线图
        # 由于ReportLab的图表功能相对简单，这里返回一个占位符
        drawing = Drawing(400, 200)
        
        # 添加图表标题
        from reportlab.graphics.shapes import String
        drawing.add(String(200, 180, "收入与利润趋势图", textAnchor='middle'))
        
        return drawing
    
    def _create_bar_chart(self, chart_data: Dict[str, Any], config: Dict[str, Any]) -> Drawing:
        """创建柱状图"""
        drawing = Drawing(400, 200)
        
        from reportlab.graphics.shapes import String
        drawing.add(String(200, 180, "财务指标对比图", textAnchor='middle'))
        
        return drawing
