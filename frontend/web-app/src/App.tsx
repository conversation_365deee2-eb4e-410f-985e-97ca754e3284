import React, { useEffect } from 'react'
import { Routes, Route, Navigate, useNavigate } from 'react-router-dom'
import { useSelector, useDispatch } from 'react-redux'
import { App as AntdApp } from 'antd'

import { RootState } from './store'
import { checkAuth } from './store/slices/authSlice'
// import Layout from './components/Layout'

// 专业的布局组件
const Layout = ({ children }: { children: React.ReactNode }) => {
  const navigate = useNavigate()
  const [collapsed, setCollapsed] = React.useState(false)
  const location = window.location

  const menuItems = [
    {
      key: '/dashboard',
      icon: '📊',
      label: '仪表板',
      path: '/dashboard'
    },
    {
      key: '/companies',
      icon: '🏢',
      label: '公司管理',
      path: '/companies'
    },
    {
      key: '/data',
      icon: '📤',
      label: '数据上传',
      path: '/data/upload'
    },
    {
      key: '/reports',
      icon: '📄',
      label: '报告管理',
      path: '/reports'
    },
    {
      key: '/shares',
      icon: '🔗',
      label: '分享管理',
      path: '/shares'
    },
    {
      key: '/settings',
      icon: '⚙️',
      label: '系统设置',
      path: '/settings'
    }
  ]

  const currentPath = location.pathname

  return (
    <div style={{ display: 'flex', minHeight: '100vh' }}>
      {/* 侧边栏 */}
      <div style={{
        width: collapsed ? '80px' : '260px',
        background: 'linear-gradient(180deg, #1e293b 0%, #334155 100%)',
        transition: 'width 0.3s ease',
        boxShadow: '2px 0 8px rgba(0,0,0,0.1)',
        position: 'relative'
      }}>
        {/* Logo区域 */}
        <div style={{
          padding: '24px 20px',
          borderBottom: '1px solid rgba(255,255,255,0.1)',
          display: 'flex',
          alignItems: 'center',
          gap: '12px'
        }}>
          <div style={{
            width: '40px',
            height: '40px',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            borderRadius: '12px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'white',
            fontWeight: 'bold',
            fontSize: '18px'
          }}>
            LF
          </div>
          {!collapsed && (
            <div>
              <div style={{ color: 'white', fontSize: '18px', fontWeight: '600' }}>
                LinkFin
              </div>
              <div style={{ color: 'rgba(255,255,255,0.6)', fontSize: '12px' }}>
                财务分析系统
              </div>
            </div>
          )}
        </div>

        {/* 导航菜单 */}
        <div style={{ padding: '20px 0' }}>
          {menuItems.map((item) => (
            <div
              key={item.key}
              onClick={() => navigate(item.path)}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '12px',
                padding: '12px 20px',
                margin: '4px 12px',
                borderRadius: '12px',
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                background: currentPath === item.path ? 'rgba(102, 126, 234, 0.2)' : 'transparent',
                color: currentPath === item.path ? '#667eea' : 'rgba(255,255,255,0.8)',
                border: currentPath === item.path ? '1px solid rgba(102, 126, 234, 0.3)' : '1px solid transparent'
              }}
              onMouseEnter={(e) => {
                if (currentPath !== item.path) {
                  e.currentTarget.style.background = 'rgba(255,255,255,0.1)'
                  e.currentTarget.style.color = 'white'
                }
              }}
              onMouseLeave={(e) => {
                if (currentPath !== item.path) {
                  e.currentTarget.style.background = 'transparent'
                  e.currentTarget.style.color = 'rgba(255,255,255,0.8)'
                }
              }}
            >
              <span style={{ fontSize: '20px' }}>{item.icon}</span>
              {!collapsed && (
                <span style={{ fontSize: '14px', fontWeight: '500' }}>
                  {item.label}
                </span>
              )}
            </div>
          ))}
        </div>

        {/* 折叠按钮 */}
        <div
          onClick={() => setCollapsed(!collapsed)}
          style={{
            position: 'absolute',
            bottom: '20px',
            left: '20px',
            right: '20px',
            padding: '12px',
            background: 'rgba(255,255,255,0.1)',
            borderRadius: '12px',
            cursor: 'pointer',
            textAlign: 'center',
            color: 'rgba(255,255,255,0.8)',
            fontSize: '14px',
            transition: 'all 0.2s ease'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.background = 'rgba(255,255,255,0.2)'
            e.currentTarget.style.color = 'white'
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.background = 'rgba(255,255,255,0.1)'
            e.currentTarget.style.color = 'rgba(255,255,255,0.8)'
          }}
        >
          {collapsed ? '👉' : '👈'} {!collapsed && '收起菜单'}
        </div>
      </div>

      {/* 主内容区域 */}
      <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
        {/* 顶部导航栏 */}
        <div style={{
          height: '70px',
          background: 'white',
          borderBottom: '1px solid #e5e7eb',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '0 24px',
          boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            <h1 style={{
              margin: 0,
              fontSize: '20px',
              fontWeight: '600',
              color: '#1f2937'
            }}>
              {menuItems.find(item => item.path === currentPath)?.label || '仪表板'}
            </h1>
          </div>

          <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            {/* 用户信息 */}
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
              <div style={{
                width: '36px',
                height: '36px',
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
                fontSize: '14px',
                fontWeight: '600'
              }}>
                A
              </div>
              <div>
                <div style={{ fontSize: '14px', fontWeight: '500', color: '#1f2937' }}>
                  管理员
                </div>
                <div style={{ fontSize: '12px', color: '#6b7280' }}>
                  <EMAIL>
                </div>
              </div>
            </div>

            {/* 退出按钮 */}
            <button
              onClick={() => {
                localStorage.clear()
                window.location.href = '/login'
              }}
              style={{
                background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',
                color: 'white',
                border: 'none',
                padding: '8px 16px',
                borderRadius: '8px',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '500',
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-1px)'
                e.currentTarget.style.boxShadow = '0 4px 12px rgba(239, 68, 68, 0.3)'
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)'
                e.currentTarget.style.boxShadow = 'none'
              }}
            >
              🚪 退出登录
            </button>
          </div>
        </div>

        {/* 页面内容 */}
        <div style={{
          flex: 1,
          background: '#f8fafc',
          overflow: 'auto'
        }}>
          {children}
        </div>
      </div>
    </div>
  )
}
import ProtectedRoute from './components/ProtectedRoute'
import LoadingSpinner from './components/LoadingSpinner'

// 页面组件
import LoginPage from './pages/auth/LoginPage'
import RegisterPage from './pages/auth/RegisterPage'
// import DashboardPage from './pages/dashboard/DashboardPage'
// import CompaniesPage from './pages/companies/CompaniesPage'
// import CompanyDetailPage from './pages/companies/CompanyDetailPage'
// import DataUploadPage from './pages/data/DataUploadPage'
// import ReportsPage from './pages/reports/ReportsPage'
// import ReportDetailPage from './pages/reports/ReportDetailPage'
// import SharePage from './pages/share/SharePage'
// import ShareViewPage from './pages/share/ShareViewPage'
// import SettingsPage from './pages/SettingsPage'
import NotFoundPage from './pages/NotFoundPage'

// 优化的公司管理页面
const CompaniesPage = () => {
  const navigate = useNavigate()
  const [companies, setCompanies] = React.useState([])
  const [loading, setLoading] = React.useState(true)
  const [showAddModal, setShowAddModal] = React.useState(false)

  // 加载公司数据
  React.useEffect(() => {
    loadCompanies()
  }, [])

  const loadCompanies = async () => {
    try {
      setLoading(true)
      // 这里应该调用真实的API
      // const response = await fetch('/api/v1/companies')
      // const data = await response.json()

      // 临时使用模拟数据，但结构与API一致
      const mockData = [
        {
          id: '1',
          name: '阿里巴巴集团',
          code: 'BABA',
          industry: '电商/云计算',
          status: '活跃',
          reports: 4,
          lastUpdate: '2024-01-15',
          revenue: '¥8,740亿',
          growth: '+12.5%',
          logo: '🛒',
          color: '#ff6b35'
        },
        {
          id: '2',
          name: '腾讯控股',
          code: 'TCEHY',
          industry: '互联网服务',
          status: '活跃',
          reports: 5,
          lastUpdate: '2024-01-16',
          revenue: '¥5,545亿',
          growth: '+8.3%',
          logo: '💬',
          color: '#4ecdc4'
        },
        {
          id: '3',
          name: '比亚迪股份',
          code: 'BYD',
          industry: '新能源汽车',
          status: '活跃',
          reports: 3,
          lastUpdate: '2024-01-17',
          revenue: '¥4,240亿',
          growth: '+15.7%',
          logo: '🚗',
          color: '#45b7d1'
        }
      ]

      setCompanies(mockData)
    } catch (error) {
      console.error('加载公司数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleAddCompany = async (companyData) => {
    try {
      // 这里应该调用真实的API
      // const response = await fetch('/api/v1/companies', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(companyData)
      // })
      // const newCompany = await response.json()

      // 模拟添加成功
      const newCompany = {
        id: Date.now().toString(),
        ...companyData,
        status: '活跃',
        reports: 0,
        lastUpdate: new Date().toISOString().split('T')[0],
        revenue: '¥0',
        growth: '0%',
        logo: '🏢',
        color: '#6c757d'
      }

      setCompanies(prev => [newCompany, ...prev])
      setShowAddModal(false)
      alert('✅ 公司添加成功！')
    } catch (error) {
      console.error('添加公司失败:', error)
      alert('❌ 添加公司失败，请重试')
    }
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面头部 */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '32px'
      }}>
        <div>
          <h2 style={{
            fontSize: '28px',
            fontWeight: '700',
            color: '#1f2937',
            margin: '0 0 8px 0'
          }}>
            🏢 公司管理
          </h2>
          <p style={{ color: '#6b7280', fontSize: '16px', margin: 0 }}>
            管理和监控所有公司的财务数据
          </p>
        </div>

        <button
          onClick={() => setShowAddModal(true)}
          style={{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            border: 'none',
            padding: '12px 20px',
            borderRadius: '12px',
            cursor: 'pointer',
            fontSize: '14px',
            fontWeight: '500',
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            boxShadow: '0 4px 12px rgba(102, 126, 234, 0.3)',
            transition: 'all 0.2s ease'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'translateY(-2px)'
            e.currentTarget.style.boxShadow = '0 6px 20px rgba(102, 126, 234, 0.4)'
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'translateY(0)'
            e.currentTarget.style.boxShadow = '0 4px 12px rgba(102, 126, 234, 0.3)'
          }}
        >
          ➕ 添加公司
        </button>
      </div>

      {/* 公司卡片网格 */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fill, minmax(400px, 1fr))',
        gap: '24px'
      }}>
        {companies.map((company) => (
          <div
            key={company.id}
            onClick={() => navigate(`/companies/${company.id}`)}
            style={{
              background: 'white',
              padding: '28px',
              borderRadius: '20px',
              border: '1px solid #e5e7eb',
              boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              position: 'relative',
              overflow: 'hidden'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'translateY(-4px)'
              e.currentTarget.style.boxShadow = '0 12px 32px rgba(0,0,0,0.1)'
              e.currentTarget.style.borderColor = company.color
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'translateY(0)'
              e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.05)'
              e.currentTarget.style.borderColor = '#e5e7eb'
            }}
          >
            {/* 装饰性背景 */}
            <div style={{
              position: 'absolute',
              top: '-20px',
              right: '-20px',
              width: '80px',
              height: '80px',
              background: `${company.color}20`,
              borderRadius: '50%'
            }} />

            {/* 公司头部信息 */}
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'flex-start',
              marginBottom: '20px',
              position: 'relative',
              zIndex: 1
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                <div style={{
                  width: '48px',
                  height: '48px',
                  background: company.color,
                  borderRadius: '12px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '24px'
                }}>
                  {company.logo}
                </div>
                <div>
                  <h3 style={{
                    margin: '0 0 4px 0',
                    fontSize: '18px',
                    fontWeight: '600',
                    color: '#1f2937'
                  }}>
                    {company.name}
                  </h3>
                  <p style={{
                    margin: 0,
                    color: '#6b7280',
                    fontSize: '14px'
                  }}>
                    {company.code} • {company.industry}
                  </p>
                </div>
              </div>

              <span style={{
                background: '#dcfce7',
                color: '#166534',
                padding: '6px 12px',
                borderRadius: '20px',
                fontSize: '12px',
                fontWeight: '500'
              }}>
                {company.status}
              </span>
            </div>

            {/* 财务数据 */}
            <div style={{
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
              gap: '16px',
              marginBottom: '20px'
            }}>
              <div style={{
                background: '#f8fafc',
                padding: '16px',
                borderRadius: '12px'
              }}>
                <div style={{ fontSize: '12px', color: '#6b7280', marginBottom: '4px' }}>
                  年营收
                </div>
                <div style={{ fontSize: '18px', fontWeight: '600', color: '#1f2937' }}>
                  {company.revenue}
                </div>
              </div>

              <div style={{
                background: '#f0fdf4',
                padding: '16px',
                borderRadius: '12px'
              }}>
                <div style={{ fontSize: '12px', color: '#6b7280', marginBottom: '4px' }}>
                  增长率
                </div>
                <div style={{
                  fontSize: '18px',
                  fontWeight: '600',
                  color: '#059669',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '4px'
                }}>
                  📈 {company.growth}
                </div>
              </div>
            </div>

            {/* 底部信息 */}
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              paddingTop: '16px',
              borderTop: '1px solid #f1f5f9'
            }}>
              <div style={{ fontSize: '12px', color: '#6b7280' }}>
                📊 {company.reports} 份报告
              </div>
              <div style={{ fontSize: '12px', color: '#6b7280' }}>
                🕒 {company.lastUpdate}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* 添加新公司的占位卡片 */}
      <div
        onClick={() => navigate('/companies/new')}
        style={{
          background: 'white',
          padding: '40px',
          borderRadius: '20px',
          border: '2px dashed #d1d5db',
          cursor: 'pointer',
          transition: 'all 0.3s ease',
          textAlign: 'center',
          marginTop: '24px'
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.borderColor = '#667eea'
          e.currentTarget.style.background = '#f8fafc'
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.borderColor = '#d1d5db'
          e.currentTarget.style.background = 'white'
        }}
      >
        <div style={{ fontSize: '48px', marginBottom: '16px' }}>➕</div>
        <h3 style={{
          fontSize: '18px',
          fontWeight: '600',
          color: '#1f2937',
          marginBottom: '8px'
        }}>
          添加新公司
        </h3>
        <p style={{ color: '#6b7280', fontSize: '14px', margin: 0 }}>
          点击这里添加新的公司进行财务分析
        </p>
      </div>

      {/* 添加公司模态框 */}
      {showAddModal && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          zIndex: 1000
        }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            padding: '32px',
            width: '500px',
            maxHeight: '80vh',
            overflow: 'auto'
          }}>
            <h2 style={{ marginBottom: '24px', color: '#1f2937' }}>添加新公司</h2>

            <form onSubmit={(e) => {
              e.preventDefault()
              const formData = new FormData(e.target as HTMLFormElement)
              const companyData = {
                name: formData.get('name') as string,
                code: formData.get('code') as string,
                industry: formData.get('industry') as string,
                description: formData.get('description') as string,
                address: formData.get('address') as string,
                contact_person: formData.get('contact_person') as string,
                contact_phone: formData.get('contact_phone') as string,
                contact_email: formData.get('contact_email') as string
              }
              handleAddCompany(companyData)
            }}>
              <div style={{ marginBottom: '16px' }}>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: '500' }}>
                  公司名称 *
                </label>
                <input
                  name="name"
                  required
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '1px solid #d1d5db',
                    borderRadius: '8px',
                    fontSize: '14px'
                  }}
                  placeholder="请输入公司名称"
                />
              </div>

              <div style={{ marginBottom: '16px' }}>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: '500' }}>
                  公司代码 *
                </label>
                <input
                  name="code"
                  required
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '1px solid #d1d5db',
                    borderRadius: '8px',
                    fontSize: '14px'
                  }}
                  placeholder="请输入公司代码"
                />
              </div>

              <div style={{ marginBottom: '16px' }}>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: '500' }}>
                  所属行业 *
                </label>
                <input
                  name="industry"
                  required
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '1px solid #d1d5db',
                    borderRadius: '8px',
                    fontSize: '14px'
                  }}
                  placeholder="请输入所属行业"
                />
              </div>

              <div style={{ marginBottom: '24px' }}>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: '500' }}>
                  公司描述
                </label>
                <textarea
                  name="description"
                  rows={3}
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '1px solid #d1d5db',
                    borderRadius: '8px',
                    fontSize: '14px',
                    resize: 'vertical'
                  }}
                  placeholder="请输入公司描述"
                />
              </div>

              <div style={{ display: 'flex', gap: '12px', justifyContent: 'flex-end' }}>
                <button
                  type="button"
                  onClick={() => setShowAddModal(false)}
                  style={{
                    padding: '12px 24px',
                    border: '1px solid #d1d5db',
                    borderRadius: '8px',
                    backgroundColor: 'white',
                    color: '#6b7280',
                    cursor: 'pointer'
                  }}
                >
                  取消
                </button>
                <button
                  type="submit"
                  style={{
                    padding: '12px 24px',
                    border: 'none',
                    borderRadius: '8px',
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    color: 'white',
                    cursor: 'pointer',
                    fontWeight: '500'
                  }}
                >
                  添加公司
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  )
}

const CompanyDetailPage = () => {
  const navigate = useNavigate()
  const [activeTab, setActiveTab] = React.useState('overview')

  // 模拟公司数据
  const companyData = {
    id: 1,
    name: '阿里巴巴集团',
    code: 'BABA',
    industry: '电商/云计算',
    logo: '🛒',
    description: '中国领先的电商和云计算公司，业务涵盖电子商务、云计算、数字媒体和娱乐等多个领域。',
    founded: '1999年',
    headquarters: '杭州',
    employees: '245,700人',
    website: 'www.alibaba.com',
    status: '活跃'
  }

  const financialData = {
    revenue: '¥8,740亿',
    profit: '¥1,740亿',
    assets: '¥12,847亿',
    equity: '¥7,234亿',
    growth: '+12.5%',
    margin: '19.9%'
  }

  const reports = [
    { id: 1, title: '2024年Q1财务分析报告', date: '2024-01-15', status: '已完成' },
    { id: 2, title: '2023年年度财务报告', date: '2024-01-10', status: '已完成' },
    { id: 3, title: '2023年Q4财务分析报告', date: '2023-12-15', status: '已完成' }
  ]

  const tabs = [
    { key: 'overview', label: '📊 概览', icon: '📊' },
    { key: 'financial', label: '💰 财务数据', icon: '💰' },
    { key: 'reports', label: '📄 报告历史', icon: '📄' },
    { key: 'analysis', label: '📈 趋势分析', icon: '📈' }
  ]

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面头部 */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '32px'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          <button
            onClick={() => navigate('/companies')}
            style={{
              background: 'white',
              border: '1px solid #e5e7eb',
              padding: '8px 12px',
              borderRadius: '8px',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            ← 返回
          </button>

          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            <div style={{
              width: '48px',
              height: '48px',
              background: '#ff6b35',
              borderRadius: '12px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '24px'
            }}>
              {companyData.logo}
            </div>
            <div>
              <h1 style={{
                fontSize: '28px',
                fontWeight: '700',
                color: '#1f2937',
                margin: 0
              }}>
                {companyData.name}
              </h1>
              <p style={{ color: '#6b7280', fontSize: '16px', margin: 0 }}>
                {companyData.code} • {companyData.industry}
              </p>
            </div>
          </div>
        </div>

        <div style={{ display: 'flex', gap: '12px' }}>
          <button
            onClick={() => navigate('/data/upload')}
            style={{
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              color: 'white',
              border: 'none',
              padding: '12px 20px',
              borderRadius: '12px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: '500'
            }}
          >
            📤 上传数据
          </button>
          <button
            onClick={() => navigate('/reports/new')}
            style={{
              background: 'white',
              color: '#667eea',
              border: '2px solid #667eea',
              padding: '12px 20px',
              borderRadius: '12px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: '500'
            }}
          >
            📊 生成报告
          </button>
        </div>
      </div>

      {/* 标签页导航 */}
      <div style={{
        background: 'white',
        borderRadius: '16px',
        padding: '20px',
        marginBottom: '24px',
        boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
      }}>
        <div style={{ display: 'flex', gap: '8px' }}>
          {tabs.map((tab) => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key)}
              style={{
                background: activeTab === tab.key ?
                  'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' : 'transparent',
                color: activeTab === tab.key ? 'white' : '#6b7280',
                border: 'none',
                padding: '12px 20px',
                borderRadius: '12px',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '500',
                transition: 'all 0.2s ease'
              }}
            >
              {tab.icon} {tab.label.replace(/^.+ /, '')}
            </button>
          ))}
        </div>
      </div>

      {/* 标签页内容 */}
      {activeTab === 'overview' && (
        <div style={{ display: 'grid', gridTemplateColumns: '2fr 1fr', gap: '24px' }}>
          {/* 公司信息 */}
          <div style={{
            background: 'white',
            borderRadius: '16px',
            padding: '32px',
            boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
          }}>
            <h3 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '20px' }}>
              🏢 公司信息
            </h3>

            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px' }}>
              {[
                { label: '成立时间', value: companyData.founded },
                { label: '总部地址', value: companyData.headquarters },
                { label: '员工人数', value: companyData.employees },
                { label: '官方网站', value: companyData.website },
                { label: '公司状态', value: companyData.status },
                { label: '所属行业', value: companyData.industry }
              ].map((item, index) => (
                <div key={index} style={{
                  padding: '16px',
                  background: '#f8fafc',
                  borderRadius: '8px'
                }}>
                  <div style={{ fontSize: '12px', color: '#6b7280', marginBottom: '4px' }}>
                    {item.label}
                  </div>
                  <div style={{ fontSize: '14px', fontWeight: '500', color: '#1f2937' }}>
                    {item.value}
                  </div>
                </div>
              ))}
            </div>

            <div style={{ marginTop: '20px' }}>
              <h4 style={{ fontSize: '16px', fontWeight: '600', marginBottom: '12px' }}>
                📝 公司简介
              </h4>
              <p style={{ color: '#6b7280', lineHeight: '1.6' }}>
                {companyData.description}
              </p>
            </div>
          </div>

          {/* 财务概览 */}
          <div style={{
            background: 'white',
            borderRadius: '16px',
            padding: '32px',
            boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
          }}>
            <h3 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '20px' }}>
              💰 财务概览
            </h3>

            {[
              { label: '年营收', value: financialData.revenue, color: '#10b981' },
              { label: '净利润', value: financialData.profit, color: '#3b82f6' },
              { label: '总资产', value: financialData.assets, color: '#8b5cf6' },
              { label: '净资产', value: financialData.equity, color: '#f59e0b' }
            ].map((item, index) => (
              <div key={index} style={{
                padding: '16px',
                background: '#f8fafc',
                borderRadius: '8px',
                marginBottom: '12px'
              }}>
                <div style={{ fontSize: '12px', color: '#6b7280', marginBottom: '4px' }}>
                  {item.label}
                </div>
                <div style={{ fontSize: '18px', fontWeight: '700', color: item.color }}>
                  {item.value}
                </div>
              </div>
            ))}

            <div style={{
              padding: '16px',
              background: 'linear-gradient(135deg, #dcfce7 0%, #f0fdf4 100%)',
              borderRadius: '8px',
              textAlign: 'center'
            }}>
              <div style={{ fontSize: '12px', color: '#166534', marginBottom: '4px' }}>
                年增长率
              </div>
              <div style={{ fontSize: '24px', fontWeight: '700', color: '#059669' }}>
                📈 {financialData.growth}
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'financial' && (
        <div style={{
          background: 'white',
          borderRadius: '16px',
          padding: '32px',
          boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
        }}>
          <h3 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '20px' }}>
            💰 详细财务数据
          </h3>

          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '20px' }}>
            {/* 盈利能力 */}
            <div>
              <h4 style={{ fontSize: '16px', fontWeight: '600', marginBottom: '16px', color: '#10b981' }}>
                📈 盈利能力
              </h4>
              {[
                { name: '营业收入', value: '¥8,740亿', change: '+12.5%' },
                { name: '净利润', value: '¥1,740亿', change: '+15.2%' },
                { name: '毛利率', value: '42.8%', change: '+1.2%' },
                { name: '净利率', value: '19.9%', change: '+0.8%' }
              ].map((item, index) => (
                <div key={index} style={{
                  padding: '12px',
                  background: '#f0fdf4',
                  borderRadius: '8px',
                  marginBottom: '8px'
                }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <span style={{ fontSize: '14px', color: '#166534' }}>{item.name}</span>
                    <span style={{ fontSize: '12px', color: '#059669', fontWeight: '500' }}>
                      {item.change}
                    </span>
                  </div>
                  <div style={{ fontSize: '16px', fontWeight: '600', color: '#1f2937' }}>
                    {item.value}
                  </div>
                </div>
              ))}
            </div>

            {/* 偿债能力 */}
            <div>
              <h4 style={{ fontSize: '16px', fontWeight: '600', marginBottom: '16px', color: '#3b82f6' }}>
                🛡️ 偿债能力
              </h4>
              {[
                { name: '流动比率', value: '1.85', change: '+0.12' },
                { name: '速动比率', value: '1.42', change: '+0.08' },
                { name: '资产负债率', value: '43.7%', change: '-1.2%' },
                { name: '利息保障倍数', value: '12.4', change: '+1.8' }
              ].map((item, index) => (
                <div key={index} style={{
                  padding: '12px',
                  background: '#eff6ff',
                  borderRadius: '8px',
                  marginBottom: '8px'
                }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <span style={{ fontSize: '14px', color: '#1e40af' }}>{item.name}</span>
                    <span style={{ fontSize: '12px', color: '#2563eb', fontWeight: '500' }}>
                      {item.change}
                    </span>
                  </div>
                  <div style={{ fontSize: '16px', fontWeight: '600', color: '#1f2937' }}>
                    {item.value}
                  </div>
                </div>
              ))}
            </div>

            {/* 运营能力 */}
            <div>
              <h4 style={{ fontSize: '16px', fontWeight: '600', marginBottom: '16px', color: '#f59e0b' }}>
                ⚡ 运营能力
              </h4>
              {[
                { name: '总资产周转率', value: '0.68', change: '+0.05' },
                { name: '存货周转率', value: '8.2', change: '+0.3' },
                { name: '应收账款周转率', value: '12.5', change: '+1.2' },
                { name: '营运资金周转率', value: '4.8', change: '+0.6' }
              ].map((item, index) => (
                <div key={index} style={{
                  padding: '12px',
                  background: '#fffbeb',
                  borderRadius: '8px',
                  marginBottom: '8px'
                }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <span style={{ fontSize: '14px', color: '#d97706' }}>{item.name}</span>
                    <span style={{ fontSize: '12px', color: '#f59e0b', fontWeight: '500' }}>
                      {item.change}
                    </span>
                  </div>
                  <div style={{ fontSize: '16px', fontWeight: '600', color: '#1f2937' }}>
                    {item.value}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {activeTab === 'reports' && (
        <div style={{
          background: 'white',
          borderRadius: '16px',
          padding: '32px',
          boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
        }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
            <h3 style={{ fontSize: '20px', fontWeight: '600', margin: 0 }}>
              📄 报告历史
            </h3>
            <button
              onClick={() => navigate('/reports/new')}
              style={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                color: 'white',
                border: 'none',
                padding: '10px 16px',
                borderRadius: '8px',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '500'
              }}
            >
              ➕ 生成新报告
            </button>
          </div>

          {reports.map((report) => (
            <div key={report.id} style={{
              padding: '20px',
              border: '1px solid #e5e7eb',
              borderRadius: '12px',
              marginBottom: '12px',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center'
            }}>
              <div>
                <h4 style={{ fontSize: '16px', fontWeight: '600', marginBottom: '4px' }}>
                  📊 {report.title}
                </h4>
                <p style={{ color: '#6b7280', fontSize: '14px', margin: 0 }}>
                  📅 {report.date}
                </p>
              </div>

              <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
                <span style={{
                  background: '#dcfce7',
                  color: '#166534',
                  padding: '4px 12px',
                  borderRadius: '20px',
                  fontSize: '12px',
                  fontWeight: '500'
                }}>
                  ✅ {report.status}
                </span>
                <button
                  onClick={() => navigate(`/reports/${report.id}`)}
                  style={{
                    background: '#3b82f6',
                    color: 'white',
                    border: 'none',
                    padding: '6px 12px',
                    borderRadius: '6px',
                    fontSize: '12px',
                    cursor: 'pointer'
                  }}
                >
                  👁️ 查看
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {activeTab === 'analysis' && (
        <div style={{
          background: 'white',
          borderRadius: '16px',
          padding: '32px',
          boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
        }}>
          <h3 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '20px' }}>
            📈 趋势分析
          </h3>

          <div style={{
            height: '300px',
            background: '#f8fafc',
            borderRadius: '12px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            marginBottom: '20px'
          }}>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '48px', marginBottom: '16px' }}>📊</div>
              <h4 style={{ fontSize: '18px', fontWeight: '600', marginBottom: '8px' }}>
                趋势图表
              </h4>
              <p style={{ color: '#6b7280' }}>
                这里将显示公司财务数据的趋势图表
              </p>
            </div>
          </div>

          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px' }}>
            <div style={{
              padding: '20px',
              background: '#f0fdf4',
              borderRadius: '12px'
            }}>
              <h4 style={{ fontSize: '16px', fontWeight: '600', marginBottom: '12px', color: '#059669' }}>
                📈 增长趋势
              </h4>
              <ul style={{ margin: 0, paddingLeft: '20px', color: '#166534' }}>
                <li>营收连续4个季度保持两位数增长</li>
                <li>净利润增长率超过营收增长率</li>
                <li>云计算业务成为新的增长引擎</li>
                <li>国际业务占比持续提升</li>
              </ul>
            </div>

            <div style={{
              padding: '20px',
              background: '#fef3c7',
              borderRadius: '12px'
            }}>
              <h4 style={{ fontSize: '16px', fontWeight: '600', marginBottom: '12px', color: '#d97706' }}>
                ⚠️ 关注要点
              </h4>
              <ul style={{ margin: 0, paddingLeft: '20px', color: '#92400e' }}>
                <li>宏观经济环境变化的影响</li>
                <li>行业竞争加剧的挑战</li>
                <li>监管政策变化的风险</li>
                <li>汇率波动对国际业务的影响</li>
              </ul>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

const DataUploadPage = () => {
  const [dragActive, setDragActive] = React.useState(false)
  const [uploadProgress, setUploadProgress] = React.useState(0)
  const [isUploading, setIsUploading] = React.useState(false)
  const [uploadedFiles, setUploadedFiles] = React.useState<any[]>([])

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(e.dataTransfer.files)
    }
  }

  const handleFiles = (files: FileList) => {
    const file = files[0]

    // 验证文件类型
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
      'application/vnd.ms-excel', // .xls
      'text/csv' // .csv
    ]

    if (!allowedTypes.includes(file.type)) {
      alert('❌ 不支持的文件格式！请上传 Excel (.xlsx, .xls) 或 CSV 文件')
      return
    }

    // 验证文件大小 (50MB)
    if (file.size > 50 * 1024 * 1024) {
      alert('❌ 文件太大！请上传小于 50MB 的文件')
      return
    }

    // 开始上传
    setIsUploading(true)
    setUploadProgress(0)

    // 模拟真实的文件上传过程
    const formData = new FormData()
    formData.append('file', file)
    formData.append('company_id', '1')
    formData.append('period_type', 'quarterly')
    formData.append('year', '2024')
    formData.append('period', '1')

    // 模拟上传进度
    const interval = setInterval(() => {
      setUploadProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval)
          setIsUploading(false)

          // 解析文件内容（模拟）
          const newFile = {
            id: Date.now(),
            name: file.name,
            size: file.size,
            type: file.type,
            uploadTime: new Date().toLocaleString(),
            status: 'processed',
            records: Math.floor(Math.random() * 1000) + 100,
            company: '阿里巴巴集团',
            period: '2024年Q1'
          }

          setUploadedFiles(prev => [...prev, newFile])

          // 显示成功消息
          alert(`✅ 文件上传成功！\n📄 文件名: ${file.name}\n📊 解析记录: ${newFile.records} 条\n🏢 关联公司: ${newFile.company}`)

          return 100
        }
        return prev + 5
      })
    }, 100)
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面头部 */}
      <div style={{ marginBottom: '32px' }}>
        <h2 style={{
          fontSize: '28px',
          fontWeight: '700',
          color: '#1f2937',
          margin: '0 0 8px 0'
        }}>
          📤 数据上传
        </h2>
        <p style={{ color: '#6b7280', fontSize: '16px', margin: 0 }}>
          上传财务数据文件进行智能分析
        </p>
      </div>

      {/* 上传区域 */}
      <div style={{
        background: 'white',
        borderRadius: '20px',
        padding: '40px',
        marginBottom: '32px',
        boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
      }}>
        <div
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
          style={{
            border: dragActive ? '3px dashed #667eea' : '3px dashed #d1d5db',
            borderRadius: '16px',
            padding: '60px 40px',
            textAlign: 'center',
            background: dragActive ? '#f0f4ff' : '#fafbfc',
            transition: 'all 0.3s ease',
            cursor: 'pointer'
          }}
          onClick={() => document.getElementById('fileInput')?.click()}
        >
          <input
            id="fileInput"
            type="file"
            multiple
            accept=".xlsx,.xls,.csv"
            style={{ display: 'none' }}
            onChange={(e) => e.target.files && handleFiles(e.target.files)}
          />

          <div style={{
            fontSize: '64px',
            marginBottom: '24px',
            opacity: dragActive ? 1 : 0.7
          }}>
            {dragActive ? '📥' : '📁'}
          </div>

          <h3 style={{
            fontSize: '24px',
            fontWeight: '600',
            color: '#1f2937',
            marginBottom: '12px'
          }}>
            {dragActive ? '释放文件开始上传' : '拖拽文件到此处或点击上传'}
          </h3>

          <p style={{
            color: '#6b7280',
            fontSize: '16px',
            marginBottom: '24px'
          }}>
            支持 Excel (.xlsx, .xls) 和 CSV 格式，单个文件最大 50MB
          </p>

          <button style={{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            border: 'none',
            padding: '14px 28px',
            borderRadius: '12px',
            cursor: 'pointer',
            fontSize: '16px',
            fontWeight: '500',
            boxShadow: '0 4px 12px rgba(102, 126, 234, 0.3)',
            transition: 'all 0.2s ease'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'translateY(-2px)'
            e.currentTarget.style.boxShadow = '0 6px 20px rgba(102, 126, 234, 0.4)'
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'translateY(0)'
            e.currentTarget.style.boxShadow = '0 4px 12px rgba(102, 126, 234, 0.3)'
          }}
          >
            📂 选择文件
          </button>
        </div>

        {/* 上传进度 */}
        {isUploading && (
          <div style={{
            marginTop: '24px',
            padding: '20px',
            background: '#f8fafc',
            borderRadius: '12px'
          }}>
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              marginBottom: '8px'
            }}>
              <span style={{ fontSize: '14px', fontWeight: '500' }}>上传中...</span>
              <span style={{ fontSize: '14px', color: '#667eea' }}>{uploadProgress}%</span>
            </div>
            <div style={{
              width: '100%',
              height: '8px',
              background: '#e5e7eb',
              borderRadius: '4px',
              overflow: 'hidden'
            }}>
              <div style={{
                width: `${uploadProgress}%`,
                height: '100%',
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                transition: 'width 0.3s ease'
              }} />
            </div>
          </div>
        )}
      </div>

      {/* 支持的文件格式 */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
        gap: '16px',
        marginBottom: '32px'
      }}>
        {[
          { icon: '📊', name: 'Excel 文件', ext: '.xlsx, .xls', color: '#10b981' },
          { icon: '📋', name: 'CSV 文件', ext: '.csv', color: '#f59e0b' },
          { icon: '📈', name: '财务报表', ext: '标准格式', color: '#8b5cf6' }
        ].map((format, index) => (
          <div key={index} style={{
            background: 'white',
            padding: '20px',
            borderRadius: '12px',
            textAlign: 'center',
            border: `2px solid ${format.color}20`,
            boxShadow: '0 2px 8px rgba(0,0,0,0.05)'
          }}>
            <div style={{ fontSize: '32px', marginBottom: '8px' }}>{format.icon}</div>
            <h4 style={{
              fontSize: '16px',
              fontWeight: '600',
              color: '#1f2937',
              marginBottom: '4px'
            }}>
              {format.name}
            </h4>
            <p style={{
              fontSize: '12px',
              color: '#6b7280',
              margin: 0
            }}>
              {format.ext}
            </p>
          </div>
        ))}
      </div>

      {/* 已上传文件列表 */}
      {uploadedFiles.length > 0 && (
        <div style={{
          background: 'white',
          borderRadius: '20px',
          padding: '24px',
          boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
        }}>
          <h3 style={{
            fontSize: '20px',
            fontWeight: '600',
            color: '#1f2937',
            marginBottom: '20px'
          }}>
            📁 已上传文件
          </h3>

          {uploadedFiles.map((file, index) => (
            <div key={file.id || index} style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              padding: '20px',
              background: '#f8fafc',
              borderRadius: '12px',
              marginBottom: '12px',
              border: '1px solid #e2e8f0'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
                <div style={{
                  fontSize: '32px',
                  background: 'white',
                  padding: '8px',
                  borderRadius: '8px',
                  boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                }}>
                  {file.type.includes('excel') || file.type.includes('spreadsheet') ? '📊' : '📋'}
                </div>
                <div>
                  <div style={{ fontWeight: '600', color: '#1f2937', fontSize: '16px' }}>
                    {file.name}
                  </div>
                  <div style={{ fontSize: '14px', color: '#6b7280', marginBottom: '4px' }}>
                    📏 {(file.size / 1024 / 1024).toFixed(2)} MB • 🕒 {file.uploadTime}
                  </div>
                  {file.records && (
                    <div style={{ fontSize: '12px', color: '#059669', fontWeight: '500' }}>
                      📊 已解析 {file.records} 条记录 • 🏢 {file.company} • 📅 {file.period}
                    </div>
                  )}
                </div>
              </div>

              <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
                <span style={{
                  background: file.status === 'processed' ? '#dcfce7' : '#fef3c7',
                  color: file.status === 'processed' ? '#166534' : '#d97706',
                  padding: '4px 12px',
                  borderRadius: '20px',
                  fontSize: '12px',
                  fontWeight: '500'
                }}>
                  {file.status === 'processed' ? '✅ 已处理' : '⏳ 处理中'}
                </span>

                {file.status === 'processed' && (
                  <button
                    onClick={() => {
                      // 生成报告功能
                      if (confirm(`📊 是否为 ${file.company} 生成财务分析报告？\n\n📄 数据文件: ${file.name}\n📅 分析期间: ${file.period}\n📊 数据记录: ${file.records} 条`)) {
                        alert('🚀 报告生成任务已启动！\n\n⏱️ 预计完成时间: 2-3分钟\n📧 完成后将发送邮件通知\n📊 您可以在"报告管理"页面查看进度')

                        // 这里可以调用真实的报告生成API
                        // generateReport(file.id, file.company)
                      }
                    }}
                    style={{
                      background: '#3b82f6',
                      color: 'white',
                      border: 'none',
                      padding: '6px 12px',
                      borderRadius: '6px',
                      fontSize: '12px',
                      cursor: 'pointer',
                      fontWeight: '500'
                    }}
                  >
                    📊 生成报告
                  </button>
                )}

                <button
                  onClick={() => {
                    if (confirm(`🗑️ 确定要删除文件 "${file.name}" 吗？\n\n⚠️ 此操作不可撤销！`)) {
                      setUploadedFiles(prev => prev.filter((_, i) => i !== index))
                      alert('✅ 文件已删除')
                    }
                  }}
                  style={{
                    background: '#ef4444',
                    color: 'white',
                    border: 'none',
                    padding: '6px 12px',
                    borderRadius: '6px',
                    fontSize: '12px',
                    cursor: 'pointer',
                    fontWeight: '500'
                  }}
                >
                  🗑️ 删除
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

const ReportsPage = () => {
  const navigate = useNavigate()
  const [selectedFilter, setSelectedFilter] = React.useState('all')

  const reports = [
    {
      id: 1,
      title: '阿里巴巴2024年Q1财务分析报告',
      company: '阿里巴巴集团',
      status: '已完成',
      date: '2024-01-15',
      type: '季度报告',
      size: '2.4 MB',
      downloads: 12,
      views: 45,
      thumbnail: '📊'
    },
    {
      id: 2,
      title: '腾讯控股2024年Q1财务分析报告',
      company: '腾讯控股',
      status: '已完成',
      date: '2024-01-16',
      type: '季度报告',
      size: '3.1 MB',
      downloads: 8,
      views: 32,
      thumbnail: '📈'
    },
    {
      id: 3,
      title: '比亚迪股份2024年Q1财务分析报告',
      company: '比亚迪股份',
      status: '处理中',
      date: '2024-01-17',
      type: '季度报告',
      size: '预计 2.8 MB',
      downloads: 0,
      views: 5,
      thumbnail: '⏳'
    },
    {
      id: 4,
      title: '阿里巴巴年度财务总结报告',
      company: '阿里巴巴集团',
      status: '已完成',
      date: '2024-01-10',
      type: '年度报告',
      size: '5.2 MB',
      downloads: 25,
      views: 78,
      thumbnail: '📋'
    }
  ]

  const filteredReports = reports.filter(report => {
    if (selectedFilter === 'all') return true
    if (selectedFilter === 'completed') return report.status === '已完成'
    if (selectedFilter === 'processing') return report.status === '处理中'
    return true
  })

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面头部 */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '32px'
      }}>
        <div>
          <h2 style={{
            fontSize: '28px',
            fontWeight: '700',
            color: '#1f2937',
            margin: '0 0 8px 0'
          }}>
            📄 报告管理
          </h2>
          <p style={{ color: '#6b7280', fontSize: '16px', margin: 0 }}>
            查看和管理所有财务分析报告
          </p>
        </div>

        <button
          onClick={() => navigate('/reports/new')}
          style={{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            border: 'none',
            padding: '12px 20px',
            borderRadius: '12px',
            cursor: 'pointer',
            fontSize: '14px',
            fontWeight: '500',
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            boxShadow: '0 4px 12px rgba(102, 126, 234, 0.3)',
            transition: 'all 0.2s ease'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'translateY(-2px)'
            e.currentTarget.style.boxShadow = '0 6px 20px rgba(102, 126, 234, 0.4)'
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'translateY(0)'
            e.currentTarget.style.boxShadow = '0 4px 12px rgba(102, 126, 234, 0.3)'
          }}
        >
          ➕ 生成新报告
        </button>
      </div>

      {/* 筛选器 */}
      <div style={{
        background: 'white',
        padding: '20px',
        borderRadius: '16px',
        marginBottom: '24px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.05)'
      }}>
        <div style={{ display: 'flex', gap: '12px', alignItems: 'center' }}>
          <span style={{ fontSize: '14px', fontWeight: '500', color: '#6b7280' }}>
            筛选：
          </span>
          {[
            { key: 'all', label: '全部报告', count: reports.length },
            { key: 'completed', label: '已完成', count: reports.filter(r => r.status === '已完成').length },
            { key: 'processing', label: '处理中', count: reports.filter(r => r.status === '处理中').length }
          ].map((filter) => (
            <button
              key={filter.key}
              onClick={() => setSelectedFilter(filter.key)}
              style={{
                background: selectedFilter === filter.key ?
                  'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' : 'transparent',
                color: selectedFilter === filter.key ? 'white' : '#6b7280',
                border: selectedFilter === filter.key ? 'none' : '1px solid #e5e7eb',
                padding: '8px 16px',
                borderRadius: '20px',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '500',
                transition: 'all 0.2s ease',
                display: 'flex',
                alignItems: 'center',
                gap: '6px'
              }}
            >
              {filter.label}
              <span style={{
                background: selectedFilter === filter.key ? 'rgba(255,255,255,0.3)' : '#f1f5f9',
                color: selectedFilter === filter.key ? 'white' : '#64748b',
                padding: '2px 6px',
                borderRadius: '10px',
                fontSize: '12px',
                fontWeight: '600'
              }}>
                {filter.count}
              </span>
            </button>
          ))}
        </div>
      </div>

      {/* 报告网格 */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fill, minmax(400px, 1fr))',
        gap: '24px'
      }}>
        {filteredReports.map((report) => (
          <div
            key={report.id}
            onClick={() => navigate(`/reports/${report.id}`)}
            style={{
              background: 'white',
              borderRadius: '20px',
              overflow: 'hidden',
              boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              border: '1px solid #f1f5f9'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'translateY(-4px)'
              e.currentTarget.style.boxShadow = '0 12px 32px rgba(0,0,0,0.1)'
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'translateY(0)'
              e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.05)'
            }}
          >
            {/* 报告缩略图区域 */}
            <div style={{
              height: '120px',
              background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '48px',
              position: 'relative'
            }}>
              {report.thumbnail}

              {/* 状态标签 */}
              <div style={{
                position: 'absolute',
                top: '12px',
                right: '12px',
                background: report.status === '已完成' ? '#dcfce7' : '#fef3c7',
                color: report.status === '已完成' ? '#166534' : '#d97706',
                padding: '4px 12px',
                borderRadius: '20px',
                fontSize: '12px',
                fontWeight: '500'
              }}>
                {report.status}
              </div>
            </div>

            {/* 报告信息 */}
            <div style={{ padding: '24px' }}>
              <h3 style={{
                fontSize: '16px',
                fontWeight: '600',
                color: '#1f2937',
                marginBottom: '8px',
                lineHeight: '1.4'
              }}>
                {report.title}
              </h3>

              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                marginBottom: '16px'
              }}>
                <span style={{
                  background: '#f1f5f9',
                  color: '#64748b',
                  padding: '4px 8px',
                  borderRadius: '12px',
                  fontSize: '12px',
                  fontWeight: '500'
                }}>
                  {report.company}
                </span>
                <span style={{
                  background: '#eff6ff',
                  color: '#2563eb',
                  padding: '4px 8px',
                  borderRadius: '12px',
                  fontSize: '12px',
                  fontWeight: '500'
                }}>
                  {report.type}
                </span>
              </div>

              {/* 统计信息 */}
              <div style={{
                display: 'grid',
                gridTemplateColumns: '1fr 1fr 1fr',
                gap: '12px',
                marginBottom: '16px'
              }}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '16px', fontWeight: '600', color: '#1f2937' }}>
                    {report.downloads}
                  </div>
                  <div style={{ fontSize: '12px', color: '#6b7280' }}>下载</div>
                </div>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '16px', fontWeight: '600', color: '#1f2937' }}>
                    {report.views}
                  </div>
                  <div style={{ fontSize: '12px', color: '#6b7280' }}>查看</div>
                </div>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '12px', fontWeight: '600', color: '#1f2937' }}>
                    {report.size}
                  </div>
                  <div style={{ fontSize: '12px', color: '#6b7280' }}>大小</div>
                </div>
              </div>

              {/* 底部操作 */}
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                paddingTop: '16px',
                borderTop: '1px solid #f1f5f9'
              }}>
                <div style={{ fontSize: '12px', color: '#6b7280' }}>
                  📅 {report.date}
                </div>

                {report.status === '已完成' && (
                  <div style={{ display: 'flex', gap: '8px' }}>
                    <button
                      onClick={(e) => {
                        e.stopPropagation()

                        // 实现下载功能
                        const downloadReport = (reportId: number, title: string) => {
                          // 显示下载选项
                          const format = confirm('📥 选择下载格式:\n\n✅ 确定 = PDF格式\n❌ 取消 = Excel格式')
                          const fileFormat = format ? 'PDF' : 'Excel'
                          const fileExt = format ? '.pdf' : '.xlsx'

                          // 模拟生成下载文件
                          const fileName = `${title.replace(/[^\w\s]/gi, '')}_${new Date().toISOString().split('T')[0]}${fileExt}`

                          // 创建模拟的文件内容
                          let content = ''
                          let mimeType = ''

                          if (format) {
                            // PDF内容模拟
                            content = `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj
2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj
3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj
4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
72 720 Td
(${title}) Tj
ET
endstream
endobj
xref
0 5
0000000000 65535 f
0000000009 00000 n
0000000058 00000 n
0000000115 00000 n
0000000206 00000 n
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
300
%%EOF`
                            mimeType = 'application/pdf'
                          } else {
                            // Excel内容模拟
                            content = `公司名称,报告期间,营业收入,净利润,总资产,净资产
${report.company},2024年Q1,1000000000,*********,5000000000,3000000000
财务指标,数值,同比增长,环比增长
营业收入增长率,12.5%,+2.3%,+1.8%
净利润增长率,15.2%,+3.1%,+2.5%
资产负债率,40%,-1.2%,-0.8%
净资产收益率,8.5%,+0.5%,+0.3%`
                            mimeType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                          }

                          // 创建并下载文件
                          const blob = new Blob([content], { type: mimeType })
                          const url = window.URL.createObjectURL(blob)
                          const link = document.createElement('a')
                          link.href = url
                          link.download = fileName
                          document.body.appendChild(link)
                          link.click()
                          document.body.removeChild(link)
                          window.URL.revokeObjectURL(url)

                          // 显示下载成功消息
                          alert(`✅ 下载成功！\n\n📄 文件名: ${fileName}\n📁 格式: ${fileFormat}\n💾 已保存到下载文件夹`)
                        }

                        downloadReport(report.id, report.title)
                      }}
                      style={{
                        background: '#10b981',
                        color: 'white',
                        border: 'none',
                        padding: '6px 12px',
                        borderRadius: '8px',
                        fontSize: '12px',
                        cursor: 'pointer',
                        fontWeight: '500'
                      }}
                    >
                      📥 下载
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation()

                        // 实现分享功能
                        const shareReport = (reportId: number, title: string) => {
                          // 生成分享链接
                          const shareId = Math.random().toString(36).substr(2, 9)
                          const shareUrl = `${window.location.origin}/share/${shareId}`

                          // 分享选项
                          const shareOptions = `🔗 报告分享链接已生成！

📄 报告: ${title}
🔗 分享链接: ${shareUrl}
🔒 访问密码: ${shareId.toUpperCase()}
⏰ 有效期: 7天
👥 访问权限: 仅查看

📋 分享方式:
1️⃣ 复制链接发送给他人
2️⃣ 扫描二维码分享
3️⃣ 发送邮件邀请

⚙️ 高级设置:
• 设置访问密码
• 限制访问次数
• 设置过期时间
• 水印保护

是否要复制链接到剪贴板？`

                          if (confirm(shareOptions)) {
                            // 复制到剪贴板
                            navigator.clipboard.writeText(shareUrl).then(() => {
                              alert(`✅ 分享链接已复制到剪贴板！\n\n🔗 ${shareUrl}\n🔒 密码: ${shareId.toUpperCase()}\n\n💡 提示: 您可以在"分享管理"页面查看和管理所有分享链接`)
                            }).catch(() => {
                              // 如果复制失败，显示链接让用户手动复制
                              prompt('📋 请手动复制分享链接:', shareUrl)
                            })

                            // 这里可以保存分享记录到后端
                            // saveShareRecord(reportId, shareId, shareUrl)
                          }
                        }

                        shareReport(report.id, report.title)
                      }}
                      style={{
                        background: '#3b82f6',
                        color: 'white',
                        border: 'none',
                        padding: '6px 12px',
                        borderRadius: '8px',
                        fontSize: '12px',
                        cursor: 'pointer',
                        fontWeight: '500'
                      }}
                    >
                      🔗 分享
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

const ReportDetailPage = () => (
  <div style={{ padding: '24px' }}>
    <h2>📊 报告详情</h2>
    <p>详细的财务分析报告内容</p>
  </div>
)

const SharePage = () => {
  const [shares, setShares] = React.useState([
    {
      id: 1,
      reportTitle: '阿里巴巴2024年Q1财务分析报告',
      shareUrl: 'https://linkfin.com/share/abc123def',
      password: 'ABC123DEF',
      createdAt: '2024-01-15 14:30',
      expiresAt: '2024-01-22 14:30',
      views: 12,
      downloads: 3,
      status: 'active'
    },
    {
      id: 2,
      reportTitle: '腾讯控股2024年Q1财务分析报告',
      shareUrl: 'https://linkfin.com/share/xyz789ghi',
      password: 'XYZ789GHI',
      createdAt: '2024-01-16 09:15',
      expiresAt: '2024-01-23 09:15',
      views: 8,
      downloads: 2,
      status: 'active'
    },
    {
      id: 3,
      reportTitle: '比亚迪股份2023年年度财务报告',
      shareUrl: 'https://linkfin.com/share/old456jkl',
      password: 'OLD456JKL',
      createdAt: '2024-01-10 16:45',
      expiresAt: '2024-01-17 16:45',
      views: 25,
      downloads: 7,
      status: 'expired'
    }
  ])

  const copyToClipboard = (text: string, type: string) => {
    navigator.clipboard.writeText(text).then(() => {
      alert(`✅ ${type}已复制到剪贴板！\n\n📋 ${text}`)
    }).catch(() => {
      prompt(`📋 请手动复制${type}:`, text)
    })
  }

  const deleteShare = (shareId: number, reportTitle: string) => {
    if (confirm(`🗑️ 确定要删除分享链接吗？\n\n📄 报告: ${reportTitle}\n⚠️ 删除后链接将立即失效，无法恢复！`)) {
      setShares(prev => prev.filter(share => share.id !== shareId))
      alert('✅ 分享链接已删除')
    }
  }

  const extendShare = (shareId: number, reportTitle: string) => {
    const days = prompt('⏰ 请输入要延长的天数 (1-30):', '7')
    if (days && !isNaN(Number(days)) && Number(days) > 0 && Number(days) <= 30) {
      const newExpiry = new Date()
      newExpiry.setDate(newExpiry.getDate() + Number(days))

      setShares(prev => prev.map(share =>
        share.id === shareId
          ? { ...share, expiresAt: newExpiry.toLocaleString('zh-CN').replace(/\//g, '-') }
          : share
      ))

      alert(`✅ 分享链接已延长 ${days} 天！\n\n📄 报告: ${reportTitle}\n⏰ 新的过期时间: ${newExpiry.toLocaleString('zh-CN')}`)
    }
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面头部 */}
      <div style={{ marginBottom: '32px' }}>
        <h2 style={{
          fontSize: '28px',
          fontWeight: '700',
          color: '#1f2937',
          margin: '0 0 8px 0'
        }}>
          🔗 分享管理
        </h2>
        <p style={{ color: '#6b7280', fontSize: '16px', margin: 0 }}>
          管理和监控所有报告分享链接
        </p>
      </div>

      {/* 统计卡片 */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
        gap: '20px',
        marginBottom: '32px'
      }}>
        {[
          { label: '总分享数', value: shares.length, icon: '🔗', color: '#3b82f6' },
          { label: '活跃分享', value: shares.filter(s => s.status === 'active').length, icon: '✅', color: '#10b981' },
          { label: '总查看数', value: shares.reduce((sum, s) => sum + s.views, 0), icon: '👁️', color: '#f59e0b' },
          { label: '总下载数', value: shares.reduce((sum, s) => sum + s.downloads, 0), icon: '📥', color: '#8b5cf6' }
        ].map((stat, index) => (
          <div key={index} style={{
            background: 'white',
            padding: '24px',
            borderRadius: '16px',
            boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '32px', marginBottom: '8px' }}>{stat.icon}</div>
            <div style={{ fontSize: '24px', fontWeight: '700', color: stat.color, marginBottom: '4px' }}>
              {stat.value}
            </div>
            <div style={{ fontSize: '14px', color: '#6b7280' }}>{stat.label}</div>
          </div>
        ))}
      </div>

      {/* 分享列表 */}
      <div style={{
        background: 'white',
        borderRadius: '16px',
        padding: '24px',
        boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
      }}>
        <h3 style={{
          fontSize: '20px',
          fontWeight: '600',
          color: '#1f2937',
          marginBottom: '20px'
        }}>
          📋 分享链接列表
        </h3>

        {shares.map((share) => (
          <div key={share.id} style={{
            padding: '20px',
            border: '1px solid #e5e7eb',
            borderRadius: '12px',
            marginBottom: '16px',
            background: share.status === 'expired' ? '#fef2f2' : '#f8fafc'
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
              <div style={{ flex: 1 }}>
                <h4 style={{
                  fontSize: '16px',
                  fontWeight: '600',
                  color: '#1f2937',
                  marginBottom: '8px'
                }}>
                  📄 {share.reportTitle}
                </h4>

                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '12px', marginBottom: '12px' }}>
                  <div>
                    <span style={{ fontSize: '12px', color: '#6b7280' }}>分享链接:</span>
                    <div style={{
                      fontSize: '14px',
                      color: '#3b82f6',
                      cursor: 'pointer',
                      textDecoration: 'underline'
                    }}
                    onClick={() => copyToClipboard(share.shareUrl, '分享链接')}
                    >
                      {share.shareUrl}
                    </div>
                  </div>

                  <div>
                    <span style={{ fontSize: '12px', color: '#6b7280' }}>访问密码:</span>
                    <div style={{
                      fontSize: '14px',
                      fontWeight: '600',
                      color: '#1f2937',
                      cursor: 'pointer'
                    }}
                    onClick={() => copyToClipboard(share.password, '访问密码')}
                    >
                      🔒 {share.password}
                    </div>
                  </div>
                </div>

                <div style={{ display: 'flex', gap: '20px', fontSize: '12px', color: '#6b7280' }}>
                  <span>🕒 创建: {share.createdAt}</span>
                  <span>⏰ 过期: {share.expiresAt}</span>
                  <span>👁️ 查看: {share.views}次</span>
                  <span>📥 下载: {share.downloads}次</span>
                </div>
              </div>

              <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
                <span style={{
                  background: share.status === 'active' ? '#dcfce7' : '#fecaca',
                  color: share.status === 'active' ? '#166534' : '#dc2626',
                  padding: '4px 12px',
                  borderRadius: '20px',
                  fontSize: '12px',
                  fontWeight: '500'
                }}>
                  {share.status === 'active' ? '✅ 活跃' : '❌ 已过期'}
                </span>

                {share.status === 'active' && (
                  <button
                    onClick={() => extendShare(share.id, share.reportTitle)}
                    style={{
                      background: '#f59e0b',
                      color: 'white',
                      border: 'none',
                      padding: '6px 12px',
                      borderRadius: '6px',
                      fontSize: '12px',
                      cursor: 'pointer',
                      fontWeight: '500'
                    }}
                  >
                    ⏰ 延期
                  </button>
                )}

                <button
                  onClick={() => deleteShare(share.id, share.reportTitle)}
                  style={{
                    background: '#ef4444',
                    color: 'white',
                    border: 'none',
                    padding: '6px 12px',
                    borderRadius: '6px',
                    fontSize: '12px',
                    cursor: 'pointer',
                    fontWeight: '500'
                  }}
                >
                  🗑️ 删除
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

const ShareViewPage = () => {
  const [isAuthenticated, setIsAuthenticated] = React.useState(false)
  const [password, setPassword] = React.useState('')
  const [shareData, setShareData] = React.useState({
    reportTitle: '阿里巴巴2024年Q1财务分析报告',
    company: '阿里巴巴集团',
    period: '2024年第一季度',
    generatedAt: '2024-01-15 14:30',
    watermark: 'LinkFin 财务分析系统 - 仅供查看'
  })

  const handlePasswordSubmit = () => {
    // 模拟密码验证
    const correctPassword = 'ABC123DEF'
    if (password.toUpperCase() === correctPassword) {
      setIsAuthenticated(true)
      alert('✅ 验证成功！正在加载报告内容...')
    } else {
      alert('❌ 访问密码错误，请重新输入')
      setPassword('')
    }
  }

  if (!isAuthenticated) {
    return (
      <div style={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '20px'
      }}>
        <div style={{
          background: 'white',
          padding: '40px',
          borderRadius: '20px',
          boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
          textAlign: 'center',
          maxWidth: '400px',
          width: '100%'
        }}>
          <div style={{ fontSize: '64px', marginBottom: '20px' }}>🔒</div>
          <h2 style={{ fontSize: '24px', fontWeight: '600', marginBottom: '12px' }}>
            访问受保护的报告
          </h2>
          <p style={{ color: '#6b7280', marginBottom: '24px' }}>
            此报告需要访问密码，请输入分享者提供的密码
          </p>

          <div style={{ marginBottom: '20px' }}>
            <input
              type="password"
              placeholder="请输入访问密码"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handlePasswordSubmit()}
              style={{
                width: '100%',
                padding: '12px',
                border: '2px solid #e5e7eb',
                borderRadius: '8px',
                fontSize: '16px',
                textAlign: 'center',
                letterSpacing: '2px'
              }}
            />
          </div>

          <button
            onClick={handlePasswordSubmit}
            style={{
              width: '100%',
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              color: 'white',
              border: 'none',
              padding: '12px',
              borderRadius: '8px',
              fontSize: '16px',
              fontWeight: '500',
              cursor: 'pointer'
            }}
          >
            🔓 验证并查看报告
          </button>

          <div style={{ marginTop: '20px', fontSize: '12px', color: '#9ca3af' }}>
            💡 提示: 密码通常为 9 位字母数字组合
          </div>
        </div>
      </div>
    )
  }

  return (
    <div style={{
      minHeight: '100vh',
      background: '#f8fafc',
      position: 'relative'
    }}>
      {/* 水印 */}
      <div style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        pointerEvents: 'none',
        zIndex: 1,
        background: `url("data:image/svg+xml,${encodeURIComponent(`
          <svg xmlns="http://www.w3.org/2000/svg" width="300" height="100" viewBox="0 0 300 100">
            <text x="150" y="50" text-anchor="middle" fill="rgba(0,0,0,0.05)" font-size="14" font-family="Arial">
              ${shareData.watermark}
            </text>
          </svg>
        `)}")`,
        backgroundRepeat: 'repeat'
      }} />

      {/* 报告内容 */}
      <div style={{ position: 'relative', zIndex: 2, padding: '24px' }}>
        {/* 报告头部 */}
        <div style={{
          background: 'white',
          padding: '32px',
          borderRadius: '16px',
          marginBottom: '24px',
          boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '48px', marginBottom: '16px' }}>📊</div>
          <h1 style={{
            fontSize: '28px',
            fontWeight: '700',
            color: '#1f2937',
            marginBottom: '8px'
          }}>
            {shareData.reportTitle}
          </h1>
          <p style={{ color: '#6b7280', fontSize: '16px', marginBottom: '16px' }}>
            {shareData.company} • {shareData.period}
          </p>
          <div style={{
            display: 'inline-block',
            background: '#f0f9ff',
            color: '#0369a1',
            padding: '8px 16px',
            borderRadius: '20px',
            fontSize: '14px'
          }}>
            📅 生成时间: {shareData.generatedAt}
          </div>
        </div>

        {/* 财务概览 */}
        <div style={{
          background: 'white',
          padding: '32px',
          borderRadius: '16px',
          marginBottom: '24px',
          boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
        }}>
          <h2 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '20px' }}>
            📈 财务概览
          </h2>

          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '20px'
          }}>
            {[
              { label: '营业收入', value: '¥2,187.4亿', change: '+12.5%', color: '#10b981' },
              { label: '净利润', value: '¥434.8亿', change: '+15.2%', color: '#3b82f6' },
              { label: '总资产', value: '¥12,847.6亿', change: '+8.7%', color: '#8b5cf6' },
              { label: '净资产', value: '¥7,234.2亿', change: '+11.3%', color: '#f59e0b' }
            ].map((item, index) => (
              <div key={index} style={{
                background: '#f8fafc',
                padding: '20px',
                borderRadius: '12px',
                textAlign: 'center'
              }}>
                <div style={{ fontSize: '12px', color: '#6b7280', marginBottom: '4px' }}>
                  {item.label}
                </div>
                <div style={{ fontSize: '20px', fontWeight: '700', color: '#1f2937', marginBottom: '4px' }}>
                  {item.value}
                </div>
                <div style={{ fontSize: '14px', color: item.color, fontWeight: '500' }}>
                  📈 {item.change}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 财务指标 */}
        <div style={{
          background: 'white',
          padding: '32px',
          borderRadius: '16px',
          marginBottom: '24px',
          boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
        }}>
          <h2 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '20px' }}>
            📊 关键财务指标
          </h2>

          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px' }}>
            <div>
              <h3 style={{ fontSize: '16px', fontWeight: '600', marginBottom: '12px' }}>
                盈利能力指标
              </h3>
              {[
                { name: '毛利率', value: '42.8%', trend: '↗️' },
                { name: '净利率', value: '19.9%', trend: '↗️' },
                { name: 'ROE', value: '15.2%', trend: '↗️' },
                { name: 'ROA', value: '8.7%', trend: '↗️' }
              ].map((item, index) => (
                <div key={index} style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  padding: '8px 0',
                  borderBottom: '1px solid #f1f5f9'
                }}>
                  <span style={{ color: '#6b7280' }}>{item.name}</span>
                  <span style={{ fontWeight: '500' }}>
                    {item.trend} {item.value}
                  </span>
                </div>
              ))}
            </div>

            <div>
              <h3 style={{ fontSize: '16px', fontWeight: '600', marginBottom: '12px' }}>
                偿债能力指标
              </h3>
              {[
                { name: '流动比率', value: '1.85', trend: '↗️' },
                { name: '速动比率', value: '1.42', trend: '↗️' },
                { name: '资产负债率', value: '43.7%', trend: '↘️' },
                { name: '利息保障倍数', value: '12.4', trend: '↗️' }
              ].map((item, index) => (
                <div key={index} style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  padding: '8px 0',
                  borderBottom: '1px solid #f1f5f9'
                }}>
                  <span style={{ color: '#6b7280' }}>{item.name}</span>
                  <span style={{ fontWeight: '500' }}>
                    {item.trend} {item.value}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* 分析结论 */}
        <div style={{
          background: 'white',
          padding: '32px',
          borderRadius: '16px',
          boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
        }}>
          <h2 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '20px' }}>
            💡 分析结论
          </h2>

          <div style={{ lineHeight: '1.6', color: '#374151' }}>
            <p style={{ marginBottom: '16px' }}>
              <strong>📈 业绩表现:</strong> 阿里巴巴集团2024年Q1财务表现强劲，营业收入同比增长12.5%，净利润增长15.2%，显示出良好的盈利能力和成长性。
            </p>
            <p style={{ marginBottom: '16px' }}>
              <strong>💪 财务健康:</strong> 公司财务结构稳健，资产负债率控制在合理水平，流动性充足，偿债能力强。
            </p>
            <p style={{ marginBottom: '16px' }}>
              <strong>🎯 投资价值:</strong> 基于当前财务指标和行业地位，公司展现出较强的投资价值和发展潜力。
            </p>
            <p style={{ marginBottom: 0 }}>
              <strong>⚠️ 风险提示:</strong> 需关注宏观经济环境变化和行业竞争加剧对公司业绩的潜在影响。
            </p>
          </div>
        </div>

        {/* 底部信息 */}
        <div style={{
          textAlign: 'center',
          padding: '20px',
          color: '#9ca3af',
          fontSize: '12px'
        }}>
          <p>📊 本报告由 LinkFin 财务分析系统生成</p>
          <p>🔒 此为分享版本，仅供查看，请勿用于商业用途</p>
          <p>💡 如需完整功能，请访问 LinkFin 官方网站</p>
        </div>
      </div>
    </div>
  )
}

const SettingsPage = () => {
  const [activeTab, setActiveTab] = React.useState('profile')
  const [settings, setSettings] = React.useState({
    username: 'admin',
    email: '<EMAIL>',
    fullName: '系统管理员',
    phone: '+86 138 0013 8000',
    notifications: true,
    emailAlerts: true,
    theme: 'light',
    language: 'zh-CN'
  })

  const tabs = [
    { key: 'profile', label: '👤 个人资料', icon: '👤' },
    { key: 'security', label: '🔒 安全设置', icon: '🔒' },
    { key: 'notifications', label: '🔔 通知设置', icon: '🔔' },
    { key: 'appearance', label: '🎨 外观设置', icon: '🎨' },
    { key: 'system', label: '⚙️ 系统设置', icon: '⚙️' }
  ]

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面头部 */}
      <div style={{ marginBottom: '32px' }}>
        <h2 style={{
          fontSize: '28px',
          fontWeight: '700',
          color: '#1f2937',
          margin: '0 0 8px 0'
        }}>
          ⚙️ 系统设置
        </h2>
        <p style={{ color: '#6b7280', fontSize: '16px', margin: 0 }}>
          管理您的账户设置和系统偏好
        </p>
      </div>

      <div style={{ display: 'flex', gap: '24px' }}>
        {/* 左侧导航 */}
        <div style={{
          width: '280px',
          background: 'white',
          borderRadius: '16px',
          padding: '20px',
          boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
          height: 'fit-content'
        }}>
          <h3 style={{
            fontSize: '16px',
            fontWeight: '600',
            color: '#1f2937',
            marginBottom: '16px'
          }}>
            设置分类
          </h3>

          {tabs.map((tab) => (
            <div
              key={tab.key}
              onClick={() => setActiveTab(tab.key)}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '12px',
                padding: '12px 16px',
                borderRadius: '12px',
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                background: activeTab === tab.key ?
                  'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' : 'transparent',
                color: activeTab === tab.key ? 'white' : '#6b7280',
                marginBottom: '4px'
              }}
              onMouseEnter={(e) => {
                if (activeTab !== tab.key) {
                  e.currentTarget.style.background = '#f8fafc'
                  e.currentTarget.style.color = '#1f2937'
                }
              }}
              onMouseLeave={(e) => {
                if (activeTab !== tab.key) {
                  e.currentTarget.style.background = 'transparent'
                  e.currentTarget.style.color = '#6b7280'
                }
              }}
            >
              <span style={{ fontSize: '18px' }}>{tab.icon}</span>
              <span style={{ fontSize: '14px', fontWeight: '500' }}>
                {tab.label.replace(/^.+ /, '')}
              </span>
            </div>
          ))}
        </div>

        {/* 右侧内容 */}
        <div style={{ flex: 1 }}>
          {/* 个人资料 */}
          {activeTab === 'profile' && (
            <div style={{
              background: 'white',
              borderRadius: '16px',
              padding: '32px',
              boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
            }}>
              <h3 style={{
                fontSize: '20px',
                fontWeight: '600',
                color: '#1f2937',
                marginBottom: '24px'
              }}>
                👤 个人资料
              </h3>

              {/* 头像区域 */}
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '20px',
                marginBottom: '32px',
                padding: '20px',
                background: '#f8fafc',
                borderRadius: '12px'
              }}>
                <div style={{
                  width: '80px',
                  height: '80px',
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'white',
                  fontSize: '32px',
                  fontWeight: 'bold'
                }}>
                  A
                </div>
                <div>
                  <h4 style={{ fontSize: '18px', fontWeight: '600', marginBottom: '4px' }}>
                    {settings.fullName}
                  </h4>
                  <p style={{ color: '#6b7280', fontSize: '14px', marginBottom: '8px' }}>
                    {settings.email}
                  </p>
                  <button style={{
                    background: 'transparent',
                    color: '#667eea',
                    border: '1px solid #667eea',
                    padding: '6px 12px',
                    borderRadius: '8px',
                    fontSize: '12px',
                    cursor: 'pointer'
                  }}>
                    📷 更换头像
                  </button>
                </div>
              </div>

              {/* 表单字段 */}
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px' }}>
                <div>
                  <label style={{
                    display: 'block',
                    marginBottom: '8px',
                    fontSize: '14px',
                    fontWeight: '500',
                    color: '#374151'
                  }}>
                    用户名
                  </label>
                  <input
                    type="text"
                    value={settings.username}
                    onChange={(e) => setSettings({...settings, username: e.target.value})}
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e5e7eb',
                      borderRadius: '8px',
                      fontSize: '14px'
                    }}
                  />
                </div>

                <div>
                  <label style={{
                    display: 'block',
                    marginBottom: '8px',
                    fontSize: '14px',
                    fontWeight: '500',
                    color: '#374151'
                  }}>
                    全名
                  </label>
                  <input
                    type="text"
                    value={settings.fullName}
                    onChange={(e) => setSettings({...settings, fullName: e.target.value})}
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e5e7eb',
                      borderRadius: '8px',
                      fontSize: '14px'
                    }}
                  />
                </div>

                <div>
                  <label style={{
                    display: 'block',
                    marginBottom: '8px',
                    fontSize: '14px',
                    fontWeight: '500',
                    color: '#374151'
                  }}>
                    邮箱地址
                  </label>
                  <input
                    type="email"
                    value={settings.email}
                    onChange={(e) => setSettings({...settings, email: e.target.value})}
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e5e7eb',
                      borderRadius: '8px',
                      fontSize: '14px'
                    }}
                  />
                </div>

                <div>
                  <label style={{
                    display: 'block',
                    marginBottom: '8px',
                    fontSize: '14px',
                    fontWeight: '500',
                    color: '#374151'
                  }}>
                    手机号码
                  </label>
                  <input
                    type="tel"
                    value={settings.phone}
                    onChange={(e) => setSettings({...settings, phone: e.target.value})}
                    style={{
                      width: '100%',
                      padding: '12px',
                      border: '2px solid #e5e7eb',
                      borderRadius: '8px',
                      fontSize: '14px'
                    }}
                  />
                </div>
              </div>

              <div style={{ marginTop: '32px', display: 'flex', gap: '12px' }}>
                <button style={{
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  color: 'white',
                  border: 'none',
                  padding: '12px 24px',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  fontSize: '14px',
                  fontWeight: '500'
                }}>
                  💾 保存更改
                </button>
                <button style={{
                  background: 'transparent',
                  color: '#6b7280',
                  border: '1px solid #e5e7eb',
                  padding: '12px 24px',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  fontSize: '14px',
                  fontWeight: '500'
                }}>
                  🔄 重置
                </button>
              </div>
            </div>
          )}

          {/* 安全设置 */}
          {activeTab === 'security' && (
            <div style={{
              background: 'white',
              borderRadius: '16px',
              padding: '32px',
              boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
            }}>
              <h3 style={{
                fontSize: '20px',
                fontWeight: '600',
                color: '#1f2937',
                marginBottom: '24px'
              }}>
                🔒 安全设置
              </h3>

              <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
                {/* 密码修改 */}
                <div style={{
                  padding: '20px',
                  background: '#f8fafc',
                  borderRadius: '12px',
                  border: '1px solid #e2e8f0'
                }}>
                  <h4 style={{ fontSize: '16px', fontWeight: '600', marginBottom: '12px' }}>
                    🔑 修改密码
                  </h4>
                  <p style={{ color: '#6b7280', fontSize: '14px', marginBottom: '16px' }}>
                    定期更新密码以保护您的账户安全
                  </p>
                  <button style={{
                    background: '#ef4444',
                    color: 'white',
                    border: 'none',
                    padding: '10px 16px',
                    borderRadius: '8px',
                    fontSize: '14px',
                    cursor: 'pointer'
                  }}>
                    🔄 更改密码
                  </button>
                </div>

                {/* 两步验证 */}
                <div style={{
                  padding: '20px',
                  background: '#f0fdf4',
                  borderRadius: '12px',
                  border: '1px solid #bbf7d0'
                }}>
                  <h4 style={{ fontSize: '16px', fontWeight: '600', marginBottom: '12px' }}>
                    📱 两步验证
                  </h4>
                  <p style={{ color: '#6b7280', fontSize: '14px', marginBottom: '16px' }}>
                    为您的账户添加额外的安全保护
                  </p>
                  <button style={{
                    background: '#10b981',
                    color: 'white',
                    border: 'none',
                    padding: '10px 16px',
                    borderRadius: '8px',
                    fontSize: '14px',
                    cursor: 'pointer'
                  }}>
                    ✅ 已启用
                  </button>
                </div>

                {/* 登录历史 */}
                <div style={{
                  padding: '20px',
                  background: '#fffbeb',
                  borderRadius: '12px',
                  border: '1px solid #fed7aa'
                }}>
                  <h4 style={{ fontSize: '16px', fontWeight: '600', marginBottom: '12px' }}>
                    📊 登录历史
                  </h4>
                  <p style={{ color: '#6b7280', fontSize: '14px', marginBottom: '16px' }}>
                    查看最近的登录活动记录
                  </p>
                  <button style={{
                    background: '#f59e0b',
                    color: 'white',
                    border: 'none',
                    padding: '10px 16px',
                    borderRadius: '8px',
                    fontSize: '14px',
                    cursor: 'pointer'
                  }}>
                    👁️ 查看历史
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* 通知设置 */}
          {activeTab === 'notifications' && (
            <div style={{
              background: 'white',
              borderRadius: '16px',
              padding: '32px',
              boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
            }}>
              <h3 style={{
                fontSize: '20px',
                fontWeight: '600',
                color: '#1f2937',
                marginBottom: '24px'
              }}>
                🔔 通知设置
              </h3>

              <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
                {[
                  { key: 'notifications', label: '系统通知', desc: '接收系统重要通知和更新' },
                  { key: 'emailAlerts', label: '邮件提醒', desc: '通过邮件接收报告完成通知' },
                  { key: 'reportAlerts', label: '报告提醒', desc: '新报告生成时发送通知' },
                  { key: 'securityAlerts', label: '安全提醒', desc: '账户安全相关的重要通知' }
                ].map((item, index) => (
                  <div key={item.key} style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    padding: '16px',
                    background: '#f8fafc',
                    borderRadius: '12px'
                  }}>
                    <div>
                      <h4 style={{ fontSize: '16px', fontWeight: '500', marginBottom: '4px' }}>
                        {item.label}
                      </h4>
                      <p style={{ color: '#6b7280', fontSize: '14px', margin: 0 }}>
                        {item.desc}
                      </p>
                    </div>
                    <label style={{
                      position: 'relative',
                      display: 'inline-block',
                      width: '50px',
                      height: '24px'
                    }}>
                      <input
                        type="checkbox"
                        checked={settings[item.key as keyof typeof settings] as boolean}
                        onChange={(e) => setSettings({...settings, [item.key]: e.target.checked})}
                        style={{ opacity: 0, width: 0, height: 0 }}
                      />
                      <span style={{
                        position: 'absolute',
                        cursor: 'pointer',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        background: settings[item.key as keyof typeof settings] ? '#667eea' : '#ccc',
                        borderRadius: '24px',
                        transition: '0.4s'
                      }}>
                        <span style={{
                          position: 'absolute',
                          content: '',
                          height: '18px',
                          width: '18px',
                          left: settings[item.key as keyof typeof settings] ? '26px' : '3px',
                          bottom: '3px',
                          background: 'white',
                          borderRadius: '50%',
                          transition: '0.4s'
                        }} />
                      </span>
                    </label>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 外观设置 */}
          {activeTab === 'appearance' && (
            <div style={{
              background: 'white',
              borderRadius: '16px',
              padding: '32px',
              boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
            }}>
              <h3 style={{
                fontSize: '20px',
                fontWeight: '600',
                color: '#1f2937',
                marginBottom: '24px'
              }}>
                🎨 外观设置
              </h3>

              <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
                {/* 主题选择 */}
                <div>
                  <h4 style={{ fontSize: '16px', fontWeight: '600', marginBottom: '12px' }}>
                    🌓 主题模式
                  </h4>
                  <div style={{ display: 'flex', gap: '12px' }}>
                    {[
                      { key: 'light', label: '☀️ 浅色', color: '#f8fafc' },
                      { key: 'dark', label: '🌙 深色', color: '#1e293b' },
                      { key: 'auto', label: '🔄 自动', color: 'linear-gradient(45deg, #f8fafc 50%, #1e293b 50%)' }
                    ].map((theme) => (
                      <div
                        key={theme.key}
                        onClick={() => setSettings({...settings, theme: theme.key})}
                        style={{
                          padding: '16px',
                          borderRadius: '12px',
                          border: settings.theme === theme.key ? '2px solid #667eea' : '2px solid #e5e7eb',
                          background: theme.color,
                          cursor: 'pointer',
                          textAlign: 'center',
                          minWidth: '100px'
                        }}
                      >
                        {theme.label}
                      </div>
                    ))}
                  </div>
                </div>

                {/* 语言选择 */}
                <div>
                  <h4 style={{ fontSize: '16px', fontWeight: '600', marginBottom: '12px' }}>
                    🌍 语言设置
                  </h4>
                  <select
                    value={settings.language}
                    onChange={(e) => setSettings({...settings, language: e.target.value})}
                    style={{
                      padding: '12px',
                      borderRadius: '8px',
                      border: '2px solid #e5e7eb',
                      fontSize: '14px',
                      width: '200px'
                    }}
                  >
                    <option value="zh-CN">🇨🇳 简体中文</option>
                    <option value="en-US">🇺🇸 English</option>
                    <option value="ja-JP">🇯🇵 日本語</option>
                  </select>
                </div>
              </div>
            </div>
          )}

          {/* 系统设置 */}
          {activeTab === 'system' && (
            <div style={{
              background: 'white',
              borderRadius: '16px',
              padding: '32px',
              boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
            }}>
              <h3 style={{
                fontSize: '20px',
                fontWeight: '600',
                color: '#1f2937',
                marginBottom: '24px'
              }}>
                ⚙️ 系统设置
              </h3>

              <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
                {/* 系统信息 */}
                <div style={{
                  padding: '20px',
                  background: '#f8fafc',
                  borderRadius: '12px'
                }}>
                  <h4 style={{ fontSize: '16px', fontWeight: '600', marginBottom: '16px' }}>
                    📊 系统信息
                  </h4>
                  <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '12px' }}>
                    <div>
                      <span style={{ color: '#6b7280', fontSize: '14px' }}>版本号：</span>
                      <span style={{ fontWeight: '500' }}>LinkFin v1.0.0</span>
                    </div>
                    <div>
                      <span style={{ color: '#6b7280', fontSize: '14px' }}>构建时间：</span>
                      <span style={{ fontWeight: '500' }}>2024-01-20</span>
                    </div>
                    <div>
                      <span style={{ color: '#6b7280', fontSize: '14px' }}>运行时间：</span>
                      <span style={{ fontWeight: '500' }}>2天 14小时</span>
                    </div>
                    <div>
                      <span style={{ color: '#6b7280', fontSize: '14px' }}>数据库：</span>
                      <span style={{ fontWeight: '500' }}>PostgreSQL 15.0</span>
                    </div>
                  </div>
                </div>

                {/* 数据管理 */}
                <div style={{
                  padding: '20px',
                  background: '#fef3c7',
                  borderRadius: '12px',
                  border: '1px solid #fed7aa'
                }}>
                  <h4 style={{ fontSize: '16px', fontWeight: '600', marginBottom: '12px' }}>
                    🗄️ 数据管理
                  </h4>
                  <p style={{ color: '#6b7280', fontSize: '14px', marginBottom: '16px' }}>
                    管理系统数据和缓存
                  </p>
                  <div style={{ display: 'flex', gap: '12px' }}>
                    <button style={{
                      background: '#f59e0b',
                      color: 'white',
                      border: 'none',
                      padding: '8px 16px',
                      borderRadius: '8px',
                      fontSize: '14px',
                      cursor: 'pointer'
                    }}>
                      🧹 清理缓存
                    </button>
                    <button style={{
                      background: '#3b82f6',
                      color: 'white',
                      border: 'none',
                      padding: '8px 16px',
                      borderRadius: '8px',
                      fontSize: '14px',
                      cursor: 'pointer'
                    }}>
                      💾 备份数据
                    </button>
                  </div>
                </div>

                {/* 危险操作 */}
                <div style={{
                  padding: '20px',
                  background: '#fef2f2',
                  borderRadius: '12px',
                  border: '1px solid #fecaca'
                }}>
                  <h4 style={{ fontSize: '16px', fontWeight: '600', marginBottom: '12px', color: '#dc2626' }}>
                    ⚠️ 危险操作
                  </h4>
                  <p style={{ color: '#6b7280', fontSize: '14px', marginBottom: '16px' }}>
                    以下操作不可逆，请谨慎操作
                  </p>
                  <div style={{ display: 'flex', gap: '12px' }}>
                    <button style={{
                      background: '#dc2626',
                      color: 'white',
                      border: 'none',
                      padding: '8px 16px',
                      borderRadius: '8px',
                      fontSize: '14px',
                      cursor: 'pointer'
                    }}>
                      🗑️ 重置系统
                    </button>
                    <button style={{
                      background: '#7f1d1d',
                      color: 'white',
                      border: 'none',
                      padding: '8px 16px',
                      borderRadius: '8px',
                      fontSize: '14px',
                      cursor: 'pointer'
                    }}>
                      💥 删除所有数据
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

// 完整的仪表板页面
const DashboardPage = () => {
  const navigate = useNavigate()

  return (
    <div style={{ padding: '24px' }}>
      {/* 欢迎区域 */}
      <div style={{ marginBottom: '32px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <h1 style={{
              fontSize: '32px',
              fontWeight: '700',
              color: '#1f2937',
              marginBottom: '8px',
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent'
            }}>
              欢迎回来！
            </h1>
            <p style={{ color: '#6b7280', fontSize: '16px', margin: 0 }}>
              今天是 {new Date().toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                weekday: 'long'
              })} 📊 让我们开始分析财务数据
            </p>
          </div>
          <div style={{ display: 'flex', gap: '12px' }}>
            <button
              onClick={() => navigate('/data/upload')}
              style={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                color: 'white',
                border: 'none',
                padding: '12px 20px',
                borderRadius: '12px',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '500',
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                boxShadow: '0 4px 12px rgba(102, 126, 234, 0.3)',
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-2px)'
                e.currentTarget.style.boxShadow = '0 6px 20px rgba(102, 126, 234, 0.4)'
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)'
                e.currentTarget.style.boxShadow = '0 4px 12px rgba(102, 126, 234, 0.3)'
              }}
            >
              📤 上传数据
            </button>
            <button
              onClick={() => navigate('/reports')}
              style={{
                background: 'white',
                color: '#667eea',
                border: '2px solid #667eea',
                padding: '12px 20px',
                borderRadius: '12px',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '500',
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = '#667eea'
                e.currentTarget.style.color = 'white'
                e.currentTarget.style.transform = 'translateY(-2px)'
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = 'white'
                e.currentTarget.style.color = '#667eea'
                e.currentTarget.style.transform = 'translateY(0)'
              }}
            >
              📊 生成报告
            </button>
          </div>
        </div>
      </div>

      {/* 统计卡片 */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
        gap: '20px',
        marginBottom: '32px'
      }}>
        {[
          {
            title: '总公司数',
            value: '3',
            change: '+1',
            changeText: '较上月',
            icon: '🏢',
            gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            shadowColor: 'rgba(102, 126, 234, 0.3)'
          },
          {
            title: '总报告数',
            value: '12',
            change: '+3',
            changeText: '本月新增',
            icon: '📊',
            gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
            shadowColor: 'rgba(240, 147, 251, 0.3)'
          },
          {
            title: '已完成报告',
            value: '8',
            change: '67%',
            changeText: '完成率',
            icon: '✅',
            gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
            shadowColor: 'rgba(79, 172, 254, 0.3)'
          },
          {
            title: '处理中报告',
            value: '4',
            change: '2小时',
            changeText: '预计完成',
            icon: '⏳',
            gradient: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
            shadowColor: 'rgba(250, 112, 154, 0.3)'
          }
        ].map((stat, index) => (
          <div
            key={index}
            style={{
              background: stat.gradient,
              padding: '28px',
              borderRadius: '20px',
              color: 'white',
              boxShadow: `0 8px 32px ${stat.shadowColor}`,
              position: 'relative',
              overflow: 'hidden',
              cursor: 'pointer',
              transition: 'all 0.3s ease'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'translateY(-4px)'
              e.currentTarget.style.boxShadow = `0 12px 40px ${stat.shadowColor}`
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'translateY(0)'
              e.currentTarget.style.boxShadow = `0 8px 32px ${stat.shadowColor}`
            }}
          >
            {/* 背景装饰 */}
            <div style={{
              position: 'absolute',
              top: '-20px',
              right: '-20px',
              width: '80px',
              height: '80px',
              background: 'rgba(255, 255, 255, 0.1)',
              borderRadius: '50%',
              opacity: 0.5
            }} />

            <div style={{ position: 'relative', zIndex: 1 }}>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'flex-start',
                marginBottom: '16px'
              }}>
                <div style={{ fontSize: '16px', opacity: 0.9, fontWeight: '500' }}>
                  {stat.title}
                </div>
                <div style={{ fontSize: '28px' }}>{stat.icon}</div>
              </div>

              <div style={{
                fontSize: '42px',
                fontWeight: '700',
                marginBottom: '12px',
                textShadow: '0 2px 4px rgba(0,0,0,0.1)'
              }}>
                {stat.value}
              </div>

              <div style={{
                fontSize: '14px',
                opacity: 0.9,
                display: 'flex',
                alignItems: 'center',
                gap: '6px'
              }}>
                <span style={{
                  background: 'rgba(255, 255, 255, 0.2)',
                  padding: '2px 8px',
                  borderRadius: '12px',
                  fontSize: '12px',
                  fontWeight: '600'
                }}>
                  {stat.change}
                </span>
                {stat.changeText}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* 主要内容区域 */}
      <div style={{ display: 'grid', gridTemplateColumns: '2fr 1fr', gap: '24px', marginBottom: '24px' }}>
        {/* 最近报告 */}
        <div style={{
          background: 'white',
          padding: '24px',
          borderRadius: '12px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
        }}>
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: '20px'
          }}>
            <h3 style={{ margin: 0, fontSize: '18px', fontWeight: '600' }}>📄 最近报告</h3>
            <button
              onClick={() => navigate('/reports')}
              style={{
                background: '#1890ff',
                color: 'white',
                border: 'none',
                padding: '6px 12px',
                borderRadius: '6px',
                cursor: 'pointer',
                fontSize: '12px'
              }}
            >
              查看全部
            </button>
          </div>

          <div style={{ space: '12px' }}>
            {[
              { name: '阿里巴巴2024年Q1财务分析', company: '阿里巴巴集团', status: '已完成', time: '2小时前' },
              { name: '腾讯控股2024年Q1财务分析', company: '腾讯控股', status: '已完成', time: '1天前' },
              { name: '比亚迪股份2024年Q1财务分析', company: '比亚迪股份', status: '处理中', time: '3小时前' }
            ].map((report, index) => (
              <div key={index} style={{
                padding: '16px',
                border: '1px solid #e5e7eb',
                borderRadius: '8px',
                marginBottom: '12px',
                cursor: 'pointer',
                transition: 'all 0.2s'
              }}
              onMouseOver={(e) => e.currentTarget.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)'}
              onMouseOut={(e) => e.currentTarget.style.boxShadow = 'none'}
              >
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <div>
                    <div style={{ fontWeight: '500', marginBottom: '4px' }}>{report.name}</div>
                    <div style={{ fontSize: '12px', color: '#6b7280' }}>{report.company}</div>
                  </div>
                  <div style={{ textAlign: 'right' }}>
                    <div style={{
                      fontSize: '12px',
                      padding: '2px 8px',
                      borderRadius: '12px',
                      background: report.status === '已完成' ? '#f0f9ff' : '#fef3c7',
                      color: report.status === '已完成' ? '#0369a1' : '#d97706',
                      marginBottom: '4px'
                    }}>
                      {report.status}
                    </div>
                    <div style={{ fontSize: '11px', color: '#9ca3af' }}>{report.time}</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 快速操作 */}
        <div style={{
          background: 'white',
          padding: '24px',
          borderRadius: '12px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
        }}>
          <h3 style={{ margin: '0 0 20px 0', fontSize: '18px', fontWeight: '600' }}>🚀 快速操作</h3>

          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            <button
              onClick={() => navigate('/companies')}
              style={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                color: 'white',
                border: 'none',
                padding: '16px',
                borderRadius: '8px',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '500',
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
              }}
            >
              🏢 管理公司
            </button>

            <button
              onClick={() => navigate('/data/upload')}
              style={{
                background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                color: 'white',
                border: 'none',
                padding: '16px',
                borderRadius: '8px',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '500',
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
              }}
            >
              📤 上传数据
            </button>

            <button
              onClick={() => navigate('/reports')}
              style={{
                background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                color: 'white',
                border: 'none',
                padding: '16px',
                borderRadius: '8px',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '500',
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
              }}
            >
              📊 生成报告
            </button>

            <button
              onClick={() => navigate('/shares')}
              style={{
                background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
                color: 'white',
                border: 'none',
                padding: '16px',
                borderRadius: '8px',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '500',
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
              }}
            >
              🔗 分享管理
            </button>
          </div>
        </div>
      </div>

      {/* 公司概览 */}
      <div style={{
        background: 'white',
        padding: '24px',
        borderRadius: '12px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
      }}>
        <h3 style={{ margin: '0 0 20px 0', fontSize: '18px', fontWeight: '600' }}>🏢 公司概览</h3>

        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '16px' }}>
          {[
            {
              name: '阿里巴巴集团',
              code: 'BABA',
              industry: '电商/云计算',
              reports: 4,
              lastUpdate: '2024-01-15',
              trend: '+12.5%',
              color: '#ff6b35'
            },
            {
              name: '腾讯控股',
              code: 'TCEHY',
              industry: '互联网服务',
              reports: 5,
              lastUpdate: '2024-01-16',
              trend: '+8.3%',
              color: '#4ecdc4'
            },
            {
              name: '比亚迪股份',
              code: 'BYD',
              industry: '新能源汽车',
              reports: 3,
              lastUpdate: '2024-01-17',
              trend: '+15.7%',
              color: '#45b7d1'
            }
          ].map((company, index) => (
            <div key={index} style={{
              border: '1px solid #e5e7eb',
              borderRadius: '8px',
              padding: '20px',
              cursor: 'pointer',
              transition: 'all 0.2s'
            }}
            onMouseOver={(e) => e.currentTarget.style.transform = 'translateY(-2px)'}
            onMouseOut={(e) => e.currentTarget.style.transform = 'translateY(0)'}
            onClick={() => navigate(`/companies/${index + 1}`)}
            >
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '12px' }}>
                <div>
                  <div style={{ fontWeight: '600', fontSize: '16px' }}>{company.name}</div>
                  <div style={{ fontSize: '12px', color: '#6b7280' }}>{company.code} • {company.industry}</div>
                </div>
                <div style={{
                  width: '12px',
                  height: '12px',
                  borderRadius: '50%',
                  background: company.color
                }}></div>
              </div>

              <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '12px', color: '#6b7280' }}>
                <span>报告数: {company.reports}</span>
                <span>更新: {company.lastUpdate}</span>
              </div>

              <div style={{
                marginTop: '8px',
                fontSize: '14px',
                color: '#059669',
                fontWeight: '500'
              }}>
                📈 {company.trend}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

const App: React.FC = () => {
  const dispatch = useDispatch()
  const { isAuthenticated, isLoading, user } = useSelector((state: RootState) => state.auth)

  useEffect(() => {
    // 检查用户认证状态
    dispatch(checkAuth() as any)
  }, [dispatch])

  // 显示加载状态
  if (isLoading) {
    return <LoadingSpinner />
  }

  return (
    <AntdApp>
      <Routes>
        {/* 公开路由 */}
        <Route path="/login" element={<LoginPage />} />
        <Route path="/register" element={<RegisterPage />} />
        <Route path="/share/:shareToken" element={<ShareViewPage />} />
        <Route path="/m/:shareToken" element={<ShareViewPage mobile />} />
        
        {/* 受保护的路由 */}
        <Route
          path="/*"
          element={
            <ProtectedRoute isAuthenticated={isAuthenticated}>
              <Layout>
                <Routes>
                  {/* 重定向根路径到仪表盘 */}
                  <Route path="/" element={<Navigate to="/dashboard" replace />} />
                  
                  {/* 主要页面 */}
                  <Route path="/dashboard" element={<DashboardPage />} />
                  
                  {/* 公司管理 */}
                  <Route path="/companies" element={<CompaniesPage />} />
                  <Route path="/companies/:companyId" element={<CompanyDetailPage />} />
                  
                  {/* 数据上传 */}
                  <Route path="/data/upload" element={<DataUploadPage />} />
                  
                  {/* 报告管理 */}
                  <Route path="/reports" element={<ReportsPage />} />
                  <Route path="/reports/:reportId" element={<ReportDetailPage />} />
                  
                  {/* 分享管理 */}
                  <Route path="/shares" element={<SharePage />} />
                  
                  {/* 设置 */}
                  <Route path="/settings" element={<SettingsPage />} />
                  
                  {/* 404页面 */}
                  <Route path="*" element={<NotFoundPage />} />
                </Routes>
              </Layout>
            </ProtectedRoute>
          }
        />
      </Routes>
    </AntdApp>
  )
}

export default App
