/* 全局样式 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol';
  font-size: 14px;
  line-height: 1.5715;
  color: rgba(0, 0, 0, 0.85);
  background-color: #fff;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New', monospace;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 布局样式 */
.app-layout {
  min-height: 100vh;
}

.app-header {
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.app-sider {
  background: #fff;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.06);
}

.app-content {
  background: #f5f5f5;
  min-height: calc(100vh - 64px);
  padding: 0;
}

/* 页面容器 */
.page-container {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100%;
}

.page-header {
  background: #fff;
  padding: 16px 24px;
  margin-bottom: 24px;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 卡片样式 */
.ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.ant-card-head {
  border-bottom: 1px solid #f0f0f0;
}

/* 表格样式 */
.ant-table {
  background: #fff;
}

.ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
}

.ant-table-tbody > tr:hover > td {
  background: #f5f5f5;
}

/* 按钮样式 */
.ant-btn {
  border-radius: 6px;
  font-weight: 500;
}

.ant-btn-primary {
  background: #1890ff;
  border-color: #1890ff;
}

.ant-btn-primary:hover {
  background: #40a9ff;
  border-color: #40a9ff;
}

/* 表单样式 */
.ant-form-item-label > label {
  font-weight: 500;
}

.ant-input,
.ant-select-selector,
.ant-picker {
  border-radius: 6px;
}

.ant-input:focus,
.ant-input-focused,
.ant-select-focused .ant-select-selector,
.ant-picker-focused {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 菜单样式 */
.ant-menu {
  border-right: none;
}

.ant-menu-item {
  border-radius: 6px;
  margin: 4px 8px;
  width: calc(100% - 16px);
}

.ant-menu-submenu {
  border-radius: 6px;
  margin: 4px 8px;
  width: calc(100% - 16px);
}

.ant-menu-item-selected {
  background: #e6f7ff;
  color: #1890ff;
}

.ant-menu-item:hover {
  background: #f5f5f5;
}

/* 标签样式 */
.ant-tag {
  border-radius: 4px;
  font-weight: 500;
}

/* 统计数字样式 */
.ant-statistic-content {
  font-weight: 600;
}

/* 模态框样式 */
.ant-modal {
  border-radius: 8px;
}

.ant-modal-header {
  border-radius: 8px 8px 0 0;
}

.ant-modal-title {
  font-weight: 600;
}

/* 消息提示样式 */
.ant-message {
  top: 80px;
}

/* 通知样式 */
.ant-notification {
  border-radius: 8px;
}

/* 加载样式 */
.ant-spin-dot {
  color: #1890ff;
}

/* 分页样式 */
.ant-pagination {
  text-align: right;
  margin-top: 16px;
}

/* 面包屑样式 */
.ant-breadcrumb {
  margin-bottom: 16px;
}

/* 工具提示样式 */
.ant-tooltip {
  border-radius: 6px;
}

/* 抽屉样式 */
.ant-drawer {
  border-radius: 8px 0 0 8px;
}

/* 步骤条样式 */
.ant-steps-item-title {
  font-weight: 500;
}

/* 时间轴样式 */
.ant-timeline-item-head {
  background: #1890ff;
}

/* 进度条样式 */
.ant-progress-text {
  font-weight: 500;
}

/* 头像样式 */
.ant-avatar {
  border: 2px solid #f0f0f0;
}

/* 徽章样式 */
.ant-badge-count {
  border-radius: 10px;
  font-weight: 500;
}

/* 警告样式 */
.ant-alert {
  border-radius: 6px;
}

/* 结果页样式 */
.ant-result {
  padding: 48px 32px;
}

/* 空状态样式 */
.ant-empty {
  margin: 32px 0;
}

/* 自定义工具类 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.mb-0 {
  margin-bottom: 0;
}

.mb-8 {
  margin-bottom: 8px;
}

.mb-16 {
  margin-bottom: 16px;
}

.mb-24 {
  margin-bottom: 24px;
}

.mt-0 {
  margin-top: 0;
}

.mt-8 {
  margin-top: 8px;
}

.mt-16 {
  margin-top: 16px;
}

.mt-24 {
  margin-top: 24px;
}

.p-0 {
  padding: 0;
}

.p-8 {
  padding: 8px;
}

.p-16 {
  padding: 16px;
}

.p-24 {
  padding: 24px;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.cursor-pointer {
  cursor: pointer;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-auto {
  overflow: auto;
}

/* 响应式样式 */
@media (max-width: 768px) {
  .page-container {
    padding: 16px;
  }
  
  .page-header {
    padding: 12px 16px;
    margin-bottom: 16px;
  }
  
  .ant-card {
    margin-bottom: 16px;
  }
  
  .ant-table {
    font-size: 12px;
  }
  
  .ant-btn {
    font-size: 12px;
    height: 32px;
    padding: 4px 8px;
  }
}

@media (max-width: 576px) {
  .page-container {
    padding: 12px;
  }
  
  .ant-card-body {
    padding: 16px;
  }
  
  .ant-form-item {
    margin-bottom: 16px;
  }
}

/* 打印样式 */
@media print {
  .ant-layout-sider,
  .ant-layout-header,
  .no-print {
    display: none !important;
  }
  
  .ant-layout-content {
    margin: 0 !important;
    padding: 0 !important;
  }
  
  .ant-card {
    box-shadow: none;
    border: 1px solid #d9d9d9;
  }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .dark-theme {
    background-color: #141414;
    color: rgba(255, 255, 255, 0.85);
  }
  
  .dark-theme .ant-card {
    background: #1f1f1f;
    border-color: #303030;
  }
  
  .dark-theme .ant-table {
    background: #1f1f1f;
  }
  
  .dark-theme .ant-table-thead > tr > th {
    background: #262626;
    color: rgba(255, 255, 255, 0.85);
  }
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

/* 加载骨架屏 */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 自定义组件样式 */
.logo {
  height: 32px;
  margin: 16px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-weight: bold;
  font-size: 16px;
}

.user-info {
  display: flex;
  align-items: center;
  padding: 0 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: rgba(0, 0, 0, 0.025);
}

.notification-badge {
  margin-right: 16px;
}

/* 状态指示器 */
.status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

.status-indicator.online {
  background-color: #52c41a;
}

.status-indicator.offline {
  background-color: #ff4d4f;
}

.status-indicator.away {
  background-color: #faad14;
}

/* 数据可视化 */
.chart-container {
  padding: 16px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.chart-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  color: rgba(0, 0, 0, 0.85);
}

/* 错误页面 */
.error-page {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
}

.error-code {
  font-size: 72px;
  font-weight: bold;
  color: #1890ff;
  margin-bottom: 16px;
}

.error-message {
  font-size: 20px;
  color: rgba(0, 0, 0, 0.65);
  margin-bottom: 24px;
}

/* 成功页面 */
.success-page {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
}

.success-icon {
  font-size: 72px;
  color: #52c41a;
  margin-bottom: 16px;
}