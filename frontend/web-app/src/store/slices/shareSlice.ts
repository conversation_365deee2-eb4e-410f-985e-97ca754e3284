import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface Share {
  id: string;
  reportId: string;
  token: string;
  expiresAt?: string;
  accessCount: number;
  maxAccess?: number;
  requirePassword: boolean;
  createdAt: string;
}

export interface ShareState {
  shares: Share[];
  currentShare: Share | null;
  loading: boolean;
  error: string | null;
}

const initialState: ShareState = {
  shares: [],
  currentShare: null,
  loading: false,
  error: null
};

const shareSlice = createSlice({
  name: 'share',
  initialState,
  reducers: {
    setShares: (state, action: PayloadAction<Share[]>) => {
      state.shares = action.payload;
    },
    setCurrentShare: (state, action: PayloadAction<Share | null>) => {
      state.currentShare = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    }
  }
});

export const { setShares, setCurrentShare, setLoading, setError } = shareSlice.actions;
export default shareSlice.reducer;