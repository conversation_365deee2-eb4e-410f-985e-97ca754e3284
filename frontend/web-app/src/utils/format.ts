/**
 * 格式化数字为货币格式
 * @param value 数值
 * @param currency 货币符号，默认为 ¥
 * @param precision 小数位数，默认为 2
 */
export const formatCurrency = (
  value: number | string,
  currency: string = '¥',
  precision: number = 2
): string => {
  const num = typeof value === 'string' ? parseFloat(value) : value;
  if (isNaN(num)) return `${currency}0.00`;
  
  return `${currency}${num.toLocaleString('zh-CN', {
    minimumFractionDigits: precision,
    maximumFractionDigits: precision
  })}`;
};

/**
 * 格式化数字为百分比
 * @param value 数值
 * @param precision 小数位数，默认为 2
 */
export const formatPercentage = (
  value: number | string,
  precision: number = 2
): string => {
  const num = typeof value === 'string' ? parseFloat(value) : value;
  if (isNaN(num)) return '0.00%';
  
  return `${(num * 100).toFixed(precision)}%`;
};

// 别名导出
export const formatPercent = formatPercentage;

/**
 * 格式化数字（添加千分位分隔符）
 * @param value 数值
 * @param precision 小数位数，默认为 2
 */
export const formatNumber = (
  value: number | string,
  precision: number = 2
): string => {
  const num = typeof value === 'string' ? parseFloat(value) : value;
  if (isNaN(num)) return '0';
  
  return num.toLocaleString('zh-CN', {
    minimumFractionDigits: precision,
    maximumFractionDigits: precision
  });
};

/**
 * 格式化大数字（万、亿）
 * @param value 数值
 * @param precision 小数位数，默认为 2
 */
export const formatLargeNumber = (
  value: number | string,
  precision: number = 2
): string => {
  const num = typeof value === 'string' ? parseFloat(value) : value;
  if (isNaN(num)) return '0';
  
  if (Math.abs(num) >= 100000000) {
    return `${(num / 100000000).toFixed(precision)}亿`;
  } else if (Math.abs(num) >= 10000) {
    return `${(num / 10000).toFixed(precision)}万`;
  }
  
  return num.toLocaleString('zh-CN');
};

/**
 * 格式化日期
 * @param date 日期
 * @param format 格式，默认为 YYYY-MM-DD
 */
export const formatDate = (
  date: Date | string | number,
  format: string = 'YYYY-MM-DD'
): string => {
  const d = new Date(date);
  if (isNaN(d.getTime())) return '';
  
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  const seconds = String(d.getSeconds()).padStart(2, '0');
  
  return format
    .replace('YYYY', String(year))
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds);
};

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @param precision 小数位数，默认为 2
 */
export const formatFileSize = (
  bytes: number,
  precision: number = 2
): string => {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(precision))} ${sizes[i]}`;
};

/**
 * 截断文本
 * @param text 文本
 * @param maxLength 最大长度
 * @param suffix 后缀，默认为 ...
 */
export const truncateText = (
  text: string,
  maxLength: number,
  suffix: string = '...'
): string => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength - suffix.length) + suffix;
};