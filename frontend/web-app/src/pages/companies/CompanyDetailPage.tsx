import React, { useEffect } from 'react';
import {
  Card,
  Descriptions,
  Button,
  Space,
  Tag,
  Divider,
  Row,
  Col,
  Statistic,
  Typography
} from 'antd';
import {
  ArrowLeftOutlined,
  EditOutlined,
  FileTextOutlined,
  ShareAltOutlined
} from '@ant-design/icons';
import { useParams, useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '@/store';
import { fetchCompanyById } from '@/store/slices/companySlice';
import { formatDate, formatCurrency } from '@/utils/format';

const { Title } = Typography;

const CompanyDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { currentCompany, loading } = useAppSelector((state: any) => state.company);

  useEffect(() => {
    if (id) {
      dispatch(fetchCompanyById(id));
    }
  }, [id, dispatch]);

  const handleBack = () => {
    navigate('/companies');
  };

  const handleEdit = () => {
    navigate(`/companies/${id}/edit`);
  };

  const handleViewReports = () => {
    navigate(`/companies/${id}/reports`);
  };

  if (!currentCompany && !loading) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Title level={3}>公司不存在</Title>
        <Button type="primary" onClick={handleBack}>
          返回公司列表
        </Button>
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <Space>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={handleBack}
          >
            返回
          </Button>
          <Title level={2} style={{ margin: 0 }}>
            {currentCompany?.name || '加载中...'}
          </Title>
        </Space>
      </div>

      {currentCompany && (
        <>
          <Row gutter={[24, 24]}>
            <Col span={16}>
              <Card
                title="基本信息"
                extra={
                  <Space>
                    <Button
                      icon={<EditOutlined />}
                      onClick={handleEdit}
                    >
                      编辑
                    </Button>
                    <Button
                      type="primary"
                      icon={<FileTextOutlined />}
                      onClick={handleViewReports}
                    >
                      查看报表
                    </Button>
                  </Space>
                }
              >
                <Descriptions column={2} bordered>
                  <Descriptions.Item label="公司名称" span={2}>
                    {currentCompany.name}
                  </Descriptions.Item>
                  <Descriptions.Item label="公司代码">
                    {currentCompany.code}
                  </Descriptions.Item>
                  <Descriptions.Item label="行业">
                    {currentCompany.industry}
                  </Descriptions.Item>
                  <Descriptions.Item label="状态">
                    <Tag color={currentCompany.status === 'active' ? 'green' : 'red'}>
                      {currentCompany.status === 'active' ? '活跃' : '非活跃'}
                    </Tag>
                  </Descriptions.Item>
                  <Descriptions.Item label="成立日期">
                    {currentCompany.establishedDate ? formatDate(currentCompany.establishedDate) : '-'}
                  </Descriptions.Item>
                  <Descriptions.Item label="法定代表人">
                    {currentCompany.legalRepresentative || '-'}
                  </Descriptions.Item>
                  <Descriptions.Item label="注册资本">
                    {currentCompany.registeredCapital ? formatCurrency(currentCompany.registeredCapital * 10000) : '-'}
                  </Descriptions.Item>
                  <Descriptions.Item label="联系电话">
                    {currentCompany.phone || '-'}
                  </Descriptions.Item>
                  <Descriptions.Item label="邮箱">
                    {currentCompany.email || '-'}
                  </Descriptions.Item>
                  <Descriptions.Item label="官网">
                    {currentCompany.website ? (
                      <a href={currentCompany.website} target="_blank" rel="noopener noreferrer">
                        {currentCompany.website}
                      </a>
                    ) : '-'}
                  </Descriptions.Item>
                  <Descriptions.Item label="公司地址" span={2}>
                    {currentCompany.address || '-'}
                  </Descriptions.Item>
                  <Descriptions.Item label="公司描述" span={2}>
                    {currentCompany.description || '-'}
                  </Descriptions.Item>
                  <Descriptions.Item label="创建时间">
                    {formatDate(currentCompany.createdAt)}
                  </Descriptions.Item>
                  <Descriptions.Item label="更新时间">
                    {formatDate(currentCompany.updatedAt)}
                  </Descriptions.Item>
                </Descriptions>
              </Card>
            </Col>

            <Col span={8}>
              <Card title="统计信息">
                <Row gutter={[16, 16]}>
                  <Col span={24}>
                    <Statistic
                      title="报表数量"
                      value={0}
                      suffix="份"
                    />
                  </Col>
                  <Col span={24}>
                    <Statistic
                      title="分享次数"
                      value={0}
                      suffix="次"
                    />
                  </Col>
                  <Col span={24}>
                    <Statistic
                      title="最近更新"
                      value={formatDate(currentCompany.updatedAt, 'YYYY-MM-DD')}
                    />
                  </Col>
                </Row>
              </Card>

              <Card title="快速操作" style={{ marginTop: '16px' }}>
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Button
                    type="primary"
                    block
                    icon={<FileTextOutlined />}
                    onClick={handleViewReports}
                  >
                    查看财务报表
                  </Button>
                  <Button
                    block
                    icon={<ShareAltOutlined />}
                    onClick={() => navigate(`/companies/${id}/shares`)}
                  >
                    管理分享
                  </Button>
                  <Button
                    block
                    icon={<EditOutlined />}
                    onClick={handleEdit}
                  >
                    编辑公司信息
                  </Button>
                </Space>
              </Card>
            </Col>
          </Row>
        </>
      )}
    </div>
  );
};

export default CompanyDetailPage;