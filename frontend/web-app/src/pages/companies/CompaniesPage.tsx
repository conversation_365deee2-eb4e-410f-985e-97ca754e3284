import React, { useEffect, useState } from 'react';
import {
  Table,
  Button,
  Space,
  Input,
  Card,
  Modal,
  Form,
  message,
  Popconfirm,
  Tag,
  Typography
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined
} from '@ant-design/icons';
import type { RootState, AppDispatch } from '@/store';
import { useAppDispatch, useAppSelector } from '@/store';
import {
  fetchCompanies,
  createCompany,
  updateCompany,
  deleteCompany,
  setCurrentCompany,
  clearError,
  type Company
} from '@/store/slices/companySlice';
import { formatDate } from '@/utils/format';

const { Title } = Typography;
const { Search } = Input;

interface CompanyFormData {
  name: string;
  code: string;
  industry: string;
  description?: string;
  establishedDate?: string;
  registeredCapital?: number;
  legalRepresentative?: string;
  address?: string;
  phone?: string;
  email?: string;
  website?: string;
}

const CompaniesPage: React.FC = () => {
  const dispatch = useAppDispatch();
  const { companies, loading, error, pagination } = useAppSelector(
    (state) => state.company
  );

  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingCompany, setEditingCompany] = useState<Company | null>(null);
  const [searchText, setSearchText] = useState('');
  const [form] = Form.useForm();

  useEffect(() => {
    dispatch(fetchCompanies());
  }, [dispatch]);

  useEffect(() => {
    if (error) {
      message.error(error);
      dispatch(clearError());
    }
  }, [error, dispatch]);

  const handleSearch = (value: string) => {
    setSearchText(value);
    dispatch(fetchCompanies({ search: value }));
  };

  const handleTableChange = (page: number, pageSize: number) => {
    dispatch(fetchCompanies({
      page,
      pageSize,
      search: searchText
    }));
  };

  const showModal = (company?: Company) => {
    setEditingCompany(company || null);
    setIsModalVisible(true);
    if (company) {
      form.setFieldsValue(company);
    } else {
      form.resetFields();
    }
  };

  const handleCancel = () => {
    setIsModalVisible(false);
    setEditingCompany(null);
    form.resetFields();
  };

  const handleSubmit = async (values: CompanyFormData) => {
    try {
      if (editingCompany) {
        await dispatch(updateCompany({
          id: editingCompany.id,
          data: values
        }));
        message.success('公司信息更新成功');
      } else {
        await dispatch(createCompany(values));
        message.success('公司创建成功');
      }
      handleCancel();
    } catch (error) {
      message.error('操作失败，请重试');
    }
  };

  const handleDelete = async (id: string) => {
    try {
      await dispatch(deleteCompany(id));
      message.success('公司删除成功');
    } catch (error) {
      message.error('删除失败，请重试');
    }
  };

  const handleView = (company: Company) => {
    dispatch(setCurrentCompany(company));
    // 这里可以导航到公司详情页面
    message.info('查看公司详情功能待实现');
  };

  const columns = [
    {
      title: '公司名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: Company) => (
        <Button type="link" onClick={() => handleView(record)}>
          {text}
        </Button>
      )
    },
    {
      title: '公司代码',
      dataIndex: 'code',
      key: 'code'
    },
    {
      title: '行业',
      dataIndex: 'industry',
      key: 'industry'
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'active' ? 'green' : 'red'}>
          {status === 'active' ? '活跃' : '非活跃'}
        </Tag>
      )
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => formatDate(date)
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: Company) => (
        <Space size="middle">
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
          >
            查看
          </Button>
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => showModal(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个公司吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={3} style={{ margin: 0 }}>公司管理</Title>
          <Space>
            <Search
              placeholder="搜索公司名称或代码"
              allowClear
              enterButton={<SearchOutlined />}
              size="middle"
              onSearch={handleSearch}
              style={{ width: 300 }}
            />
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => showModal()}
            >
              新增公司
            </Button>
          </Space>
        </div>

        <Table
          columns={columns}
          dataSource={companies}
          rowKey="id"
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            onChange: handleTableChange
          }}
        />
      </Card>

      <Modal
        title={editingCompany ? '编辑公司' : '新增公司'}
        open={isModalVisible}
        onCancel={handleCancel}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="name"
            label="公司名称"
            rules={[{ required: true, message: '请输入公司名称' }]}
          >
            <Input placeholder="请输入公司名称" />
          </Form.Item>

          <Form.Item
            name="code"
            label="公司代码"
            rules={[{ required: true, message: '请输入公司代码' }]}
          >
            <Input placeholder="请输入公司代码" />
          </Form.Item>

          <Form.Item
            name="industry"
            label="行业"
            rules={[{ required: true, message: '请输入行业' }]}
          >
            <Input placeholder="请输入行业" />
          </Form.Item>

          <Form.Item
            name="description"
            label="公司描述"
          >
            <Input.TextArea rows={3} placeholder="请输入公司描述" />
          </Form.Item>

          <Form.Item
            name="legalRepresentative"
            label="法定代表人"
          >
            <Input placeholder="请输入法定代表人" />
          </Form.Item>

          <Form.Item
            name="registeredCapital"
            label="注册资本（万元）"
          >
            <Input type="number" placeholder="请输入注册资本" />
          </Form.Item>

          <Form.Item
            name="address"
            label="公司地址"
          >
            <Input placeholder="请输入公司地址" />
          </Form.Item>

          <Form.Item
            name="phone"
            label="联系电话"
          >
            <Input placeholder="请输入联系电话" />
          </Form.Item>

          <Form.Item
            name="email"
            label="邮箱"
            rules={[{ type: 'email', message: '请输入有效的邮箱地址' }]}
          >
            <Input placeholder="请输入邮箱" />
          </Form.Item>

          <Form.Item
            name="website"
            label="官网"
          >
            <Input placeholder="请输入官网地址" />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={handleCancel}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                {editingCompany ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default CompaniesPage;