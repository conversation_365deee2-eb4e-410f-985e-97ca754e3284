import React, { useState } from 'react';
import {
  Card,
  Upload,
  Button,
  message,
  Progress,
  Table,
  Space,
  Tag,
  Typography,
  Row,
  Col,
  Alert,
  Divider
} from 'antd';
import {
  InboxOutlined,
  UploadOutlined,
  FileExcelOutlined,
  DeleteOutlined,
  EyeOutlined
} from '@ant-design/icons';
import type { UploadProps, UploadFile } from 'antd';
import { useAppDispatch, useAppSelector } from '@/store';
import { formatFileSize, formatDate } from '@/utils/format';

const { Dragger } = Upload;
const { Title, Text } = Typography;

interface UploadRecord {
  id: string;
  fileName: string;
  fileSize: number;
  uploadTime: string;
  status: 'uploading' | 'success' | 'error';
  progress: number;
  errorMessage?: string;
}

const DataUploadPage: React.FC = () => {
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [uploadRecords, setUploadRecords] = useState<UploadRecord[]>([]);
  const [uploading, setUploading] = useState(false);
  const dispatch = useAppDispatch();

  const uploadProps: UploadProps = {
    name: 'file',
    multiple: true,
    accept: '.xlsx,.xls,.csv',
    fileList,
    beforeUpload: (file) => {
      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                     file.type === 'application/vnd.ms-excel' ||
                     file.type === 'text/csv';
      if (!isExcel) {
        message.error('只能上传 Excel 或 CSV 文件！');
        return false;
      }
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        message.error('文件大小不能超过 10MB！');
        return false;
      }
      return false; // 阻止自动上传
    },
    onChange: (info) => {
      setFileList(info.fileList);
    },
    onDrop: (e) => {
      console.log('Dropped files', e.dataTransfer.files);
    },
  };

  const handleUpload = async () => {
    if (fileList.length === 0) {
      message.warning('请先选择要上传的文件');
      return;
    }

    setUploading(true);
    
    try {
      for (const file of fileList) {
        const uploadRecord: UploadRecord = {
          id: Date.now().toString() + Math.random(),
          fileName: file.name,
          fileSize: file.size || 0,
          uploadTime: new Date().toISOString(),
          status: 'uploading',
          progress: 0
        };
        
        setUploadRecords(prev => [uploadRecord, ...prev]);
        
        // 模拟上传进度
        const formData = new FormData();
        formData.append('file', file.originFileObj as File);
        
        try {
          // 这里应该调用实际的上传API
          // const response = await api.post('/data/upload', formData, {
          //   onUploadProgress: (progressEvent) => {
          //     const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          //     setUploadRecords(prev => 
          //       prev.map(record => 
          //         record.id === uploadRecord.id 
          //           ? { ...record, progress }
          //           : record
          //       )
          //     );
          //   }
          // });
          
          // 模拟上传过程
          for (let progress = 0; progress <= 100; progress += 10) {
            await new Promise(resolve => setTimeout(resolve, 100));
            setUploadRecords(prev => 
              prev.map(record => 
                record.id === uploadRecord.id 
                  ? { ...record, progress }
                  : record
              )
            );
          }
          
          setUploadRecords(prev => 
            prev.map(record => 
              record.id === uploadRecord.id 
                ? { ...record, status: 'success', progress: 100 }
                : record
            )
          );
          
        } catch (error) {
          setUploadRecords(prev => 
            prev.map(record => 
              record.id === uploadRecord.id 
                ? { 
                    ...record, 
                    status: 'error', 
                    errorMessage: '上传失败，请重试'
                  }
                : record
            )
          );
        }
      }
      
      message.success('文件上传完成');
      setFileList([]);
    } catch (error) {
      message.error('上传失败，请重试');
    } finally {
      setUploading(false);
    }
  };

  const handleRemoveFile = (file: UploadFile) => {
    setFileList(prev => prev.filter(item => item.uid !== file.uid));
  };

  const handleDeleteRecord = (recordId: string) => {
    setUploadRecords(prev => prev.filter(record => record.id !== recordId));
  };

  const columns = [
    {
      title: '文件名',
      dataIndex: 'fileName',
      key: 'fileName',
      render: (text: string) => (
        <Space>
          <FileExcelOutlined style={{ color: '#52c41a' }} />
          <Text>{text}</Text>
        </Space>
      ),
    },
    {
      title: '文件大小',
      dataIndex: 'fileSize',
      key: 'fileSize',
      render: (size: number) => formatFileSize(size),
    },
    {
      title: '上传时间',
      dataIndex: 'uploadTime',
      key: 'uploadTime',
      render: (time: string) => formatDate(time),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string, record: UploadRecord) => {
        if (status === 'uploading') {
          return (
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <Tag color="processing">上传中</Tag>
              <Progress percent={record.progress} size="small" />
            </Space>
          );
        }
        if (status === 'success') {
          return <Tag color="success">上传成功</Tag>;
        }
        if (status === 'error') {
          return (
            <Space direction="vertical" size="small">
              <Tag color="error">上传失败</Tag>
              {record.errorMessage && (
                <Text type="danger" style={{ fontSize: '12px' }}>
                  {record.errorMessage}
                </Text>
              )}
            </Space>
          );
        }
        return null;
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: UploadRecord) => (
        <Space>
          <Button
            type="link"
            icon={<EyeOutlined />}
            size="small"
            disabled={record.status !== 'success'}
          >
            查看
          </Button>
          <Button
            type="link"
            icon={<DeleteOutlined />}
            size="small"
            danger
            onClick={() => handleDeleteRecord(record.id)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>数据上传</Title>
      
      <Row gutter={[24, 24]}>
        <Col span={24}>
          <Card title="文件上传">
            <Alert
              message="上传说明"
              description="支持上传 Excel (.xlsx, .xls) 和 CSV 文件，单个文件大小不超过 10MB。请确保数据格式正确，包含必要的字段信息。"
              type="info"
              showIcon
              style={{ marginBottom: '16px' }}
            />
            
            <Dragger {...uploadProps} style={{ marginBottom: '16px' }}>
              <p className="ant-upload-drag-icon">
                <InboxOutlined />
              </p>
              <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
              <p className="ant-upload-hint">
                支持单个或批量上传。严禁上传公司数据或其他敏感信息。
              </p>
            </Dragger>
            
            <Space>
              <Button
                type="primary"
                icon={<UploadOutlined />}
                onClick={handleUpload}
                loading={uploading}
                disabled={fileList.length === 0}
              >
                开始上传
              </Button>
              <Button
                onClick={() => setFileList([])}
                disabled={fileList.length === 0 || uploading}
              >
                清空列表
              </Button>
            </Space>
          </Card>
        </Col>
        
        <Col span={24}>
          <Card title="上传记录">
            <Table
              columns={columns}
              dataSource={uploadRecords}
              rowKey="id"
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`,
              }}
              locale={{
                emptyText: '暂无上传记录'
              }}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default DataUploadPage;