import React, { useState } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Switch,
  Select,
  Typography,
  Row,
  Col,
  Divider,
  message,
  Upload,
  Avatar,
  Space,
  Tabs,
  Alert,
  Modal,
  Table,
  Tag,
  Popconfirm
} from 'antd';
import {
  UserOutlined,
  SettingOutlined,
  SecurityScanOutlined,
  BellOutlined,
  TeamOutlined,
  UploadOutlined,
  EditOutlined,
  DeleteOutlined,
  PlusOutlined,
  KeyOutlined
} from '@ant-design/icons';
import type { UploadProps } from 'antd';

const { Title, Text } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;
const { TextArea } = Input;

interface UserInfo {
  id: string;
  username: string;
  email: string;
  fullName: string;
  phone: string;
  department: string;
  position: string;
  avatar?: string;
  bio?: string;
}

interface SystemSettings {
  theme: 'light' | 'dark' | 'auto';
  language: string;
  timezone: string;
  dateFormat: string;
  currency: string;
  autoSave: boolean;
  emailNotifications: boolean;
  smsNotifications: boolean;
  desktopNotifications: boolean;
}

interface TeamMember {
  id: string;
  name: string;
  email: string;
  role: string;
  department: string;
  status: 'active' | 'inactive';
  lastLogin: string;
  createdAt: string;
}

const SettingsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('profile');
  const [loading, setLoading] = useState(false);
  const [userForm] = Form.useForm();
  const [systemForm] = Form.useForm();
  const [passwordForm] = Form.useForm();
  const [teamModalVisible, setTeamModalVisible] = useState(false);
  const [editingMember, setEditingMember] = useState<TeamMember | null>(null);
  const [memberForm] = Form.useForm();

  // 模拟用户数据
  const [userInfo, setUserInfo] = useState<UserInfo>({
    id: '1',
    username: 'admin',
    email: '<EMAIL>',
    fullName: '系统管理员',
    phone: '13800138000',
    department: '技术部',
    position: '系统管理员',
    bio: '负责系统维护和用户管理'
  });

  // 模拟系统设置
  const [systemSettings, setSystemSettings] = useState<SystemSettings>({
    theme: 'light',
    language: 'zh-CN',
    timezone: 'Asia/Shanghai',
    dateFormat: 'YYYY-MM-DD',
    currency: 'CNY',
    autoSave: true,
    emailNotifications: true,
    smsNotifications: false,
    desktopNotifications: true
  });

  // 模拟团队成员数据
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([
    {
      id: '1',
      name: '张三',
      email: '<EMAIL>',
      role: '管理员',
      department: '财务部',
      status: 'active',
      lastLogin: '2024-01-15T10:30:00Z',
      createdAt: '2024-01-01T09:00:00Z'
    },
    {
      id: '2',
      name: '李四',
      email: '<EMAIL>',
      role: '用户',
      department: '财务部',
      status: 'active',
      lastLogin: '2024-01-14T16:45:00Z',
      createdAt: '2024-01-02T10:00:00Z'
    },
    {
      id: '3',
      name: '王五',
      email: '<EMAIL>',
      role: '用户',
      department: '审计部',
      status: 'inactive',
      lastLogin: '2024-01-10T14:20:00Z',
      createdAt: '2024-01-03T11:00:00Z'
    }
  ]);

  const handleUserInfoSave = async (values: any) => {
    try {
      setLoading(true);
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      setUserInfo({ ...userInfo, ...values });
      message.success('个人信息更新成功');
    } catch (error) {
      message.error('更新失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const handleSystemSettingsSave = async (values: any) => {
    try {
      setLoading(true);
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      setSystemSettings({ ...systemSettings, ...values });
      message.success('系统设置更新成功');
    } catch (error) {
      message.error('更新失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const handlePasswordChange = async (values: any) => {
    try {
      setLoading(true);
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      message.success('密码修改成功');
      passwordForm.resetFields();
    } catch (error) {
      message.error('密码修改失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const handleAvatarUpload: UploadProps['customRequest'] = (options) => {
    // 模拟上传
    setTimeout(() => {
      message.success('头像上传成功');
      if (options.onSuccess) {
        options.onSuccess({}, new XMLHttpRequest());
      }
    }, 1000);
  };

  const handleAddMember = () => {
    setEditingMember(null);
    memberForm.resetFields();
    setTeamModalVisible(true);
  };

  const handleEditMember = (member: TeamMember) => {
    setEditingMember(member);
    memberForm.setFieldsValue(member);
    setTeamModalVisible(true);
  };

  const handleDeleteMember = (memberId: string) => {
    setTeamMembers(teamMembers.filter(member => member.id !== memberId));
    message.success('成员删除成功');
  };

  const handleMemberModalOk = async () => {
    try {
      const values = await memberForm.validateFields();
      
      if (editingMember) {
        // 更新成员
        setTeamMembers(teamMembers.map(member => 
          member.id === editingMember.id ? { ...member, ...values } : member
        ));
        message.success('成员信息更新成功');
      } else {
        // 添加成员
        const newMember: TeamMember = {
          id: Date.now().toString(),
          ...values,
          status: 'active',
          lastLogin: '',
          createdAt: new Date().toISOString()
        };
        setTeamMembers([...teamMembers, newMember]);
        message.success('成员添加成功');
      }
      
      setTeamModalVisible(false);
      memberForm.resetFields();
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const teamColumns = [
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      render: (role: string) => (
        <Tag color={role === '管理员' ? 'red' : 'blue'}>{role}</Tag>
      ),
    },
    {
      title: '部门',
      dataIndex: 'department',
      key: 'department',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'active' ? 'green' : 'gray'}>
          {status === 'active' ? '活跃' : '非活跃'}
        </Tag>
      ),
    },
    {
      title: '最后登录',
      dataIndex: 'lastLogin',
      key: 'lastLogin',
      render: (date: string) => date ? new Date(date).toLocaleString() : '从未登录',
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: TeamMember) => (
        <Space>
          <Button
            type="link"
            icon={<EditOutlined />}
            size="small"
            onClick={() => handleEditMember(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个成员吗？"
            onConfirm={() => handleDeleteMember(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              icon={<DeleteOutlined />}
              size="small"
              danger
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>系统设置</Title>
      
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        {/* 个人资料 */}
        <TabPane
          tab={
            <span>
              <UserOutlined />
              个人资料
            </span>
          }
          key="profile"
        >
          <Row gutter={[24, 24]}>
            <Col span={8}>
              <Card title="头像设置">
                <div style={{ textAlign: 'center' }}>
                  <Avatar
                    size={120}
                    icon={<UserOutlined />}
                    src={userInfo.avatar}
                    style={{ marginBottom: '16px' }}
                  />
                  <br />
                  <Upload
                    customRequest={handleAvatarUpload}
                    showUploadList={false}
                    accept="image/*"
                  >
                    <Button icon={<UploadOutlined />}>更换头像</Button>
                  </Upload>
                </div>
              </Card>
            </Col>
            
            <Col span={16}>
              <Card title="基本信息">
                <Form
                  form={userForm}
                  layout="vertical"
                  initialValues={userInfo}
                  onFinish={handleUserInfoSave}
                >
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        name="fullName"
                        label="姓名"
                        rules={[{ required: true, message: '请输入姓名' }]}
                      >
                        <Input />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name="username"
                        label="用户名"
                        rules={[{ required: true, message: '请输入用户名' }]}
                      >
                        <Input />
                      </Form.Item>
                    </Col>
                  </Row>
                  
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        name="email"
                        label="邮箱"
                        rules={[
                          { required: true, message: '请输入邮箱' },
                          { type: 'email', message: '请输入有效的邮箱地址' }
                        ]}
                      >
                        <Input />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name="phone"
                        label="手机号"
                        rules={[{ required: true, message: '请输入手机号' }]}
                      >
                        <Input />
                      </Form.Item>
                    </Col>
                  </Row>
                  
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        name="department"
                        label="部门"
                        rules={[{ required: true, message: '请输入部门' }]}
                      >
                        <Input />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name="position"
                        label="职位"
                        rules={[{ required: true, message: '请输入职位' }]}
                      >
                        <Input />
                      </Form.Item>
                    </Col>
                  </Row>
                  
                  <Form.Item name="bio" label="个人简介">
                    <TextArea rows={4} placeholder="请输入个人简介" />
                  </Form.Item>
                  
                  <Form.Item>
                    <Button type="primary" htmlType="submit" loading={loading}>
                      保存更改
                    </Button>
                  </Form.Item>
                </Form>
              </Card>
            </Col>
          </Row>
        </TabPane>

        {/* 系统设置 */}
        <TabPane
          tab={
            <span>
              <SettingOutlined />
              系统设置
            </span>
          }
          key="system"
        >
          <Card title="系统偏好设置">
            <Form
              form={systemForm}
              layout="vertical"
              initialValues={systemSettings}
              onFinish={handleSystemSettingsSave}
            >
              <Row gutter={[24, 24]}>
                <Col span={12}>
                  <Form.Item name="theme" label="主题">
                    <Select>
                      <Option value="light">浅色主题</Option>
                      <Option value="dark">深色主题</Option>
                      <Option value="auto">跟随系统</Option>
                    </Select>
                  </Form.Item>
                </Col>
                
                <Col span={12}>
                  <Form.Item name="language" label="语言">
                    <Select>
                      <Option value="zh-CN">简体中文</Option>
                      <Option value="en-US">English</Option>
                    </Select>
                  </Form.Item>
                </Col>
                
                <Col span={12}>
                  <Form.Item name="timezone" label="时区">
                    <Select>
                      <Option value="Asia/Shanghai">北京时间 (UTC+8)</Option>
                      <Option value="America/New_York">纽约时间 (UTC-5)</Option>
                      <Option value="Europe/London">伦敦时间 (UTC+0)</Option>
                    </Select>
                  </Form.Item>
                </Col>
                
                <Col span={12}>
                  <Form.Item name="dateFormat" label="日期格式">
                    <Select>
                      <Option value="YYYY-MM-DD">2024-01-15</Option>
                      <Option value="DD/MM/YYYY">15/01/2024</Option>
                      <Option value="MM/DD/YYYY">01/15/2024</Option>
                    </Select>
                  </Form.Item>
                </Col>
                
                <Col span={12}>
                  <Form.Item name="currency" label="默认货币">
                    <Select>
                      <Option value="CNY">人民币 (CNY)</Option>
                      <Option value="USD">美元 (USD)</Option>
                      <Option value="EUR">欧元 (EUR)</Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>
              
              <Divider>功能设置</Divider>
              
              <Row gutter={[24, 24]}>
                <Col span={24}>
                  <Form.Item name="autoSave" valuePropName="checked">
                    <Space>
                      <Switch />
                      <span>自动保存</span>
                      <Text type="secondary">编辑时自动保存内容</Text>
                    </Space>
                  </Form.Item>
                </Col>
              </Row>
              
              <Form.Item>
                <Button type="primary" htmlType="submit" loading={loading}>
                  保存设置
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </TabPane>

        {/* 安全设置 */}
        <TabPane
          tab={
            <span>
              <SecurityScanOutlined />
              安全设置
            </span>
          }
          key="security"
        >
          <Card title="密码修改">
            <Form
              form={passwordForm}
              layout="vertical"
              onFinish={handlePasswordChange}
              style={{ maxWidth: '400px' }}
            >
              <Form.Item
                name="currentPassword"
                label="当前密码"
                rules={[{ required: true, message: '请输入当前密码' }]}
              >
                <Input.Password />
              </Form.Item>
              
              <Form.Item
                name="newPassword"
                label="新密码"
                rules={[
                  { required: true, message: '请输入新密码' },
                  { min: 6, message: '密码至少6位' }
                ]}
              >
                <Input.Password />
              </Form.Item>
              
              <Form.Item
                name="confirmPassword"
                label="确认新密码"
                dependencies={['newPassword']}
                rules={[
                  { required: true, message: '请确认新密码' },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (!value || getFieldValue('newPassword') === value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error('两次输入的密码不一致'));
                    },
                  }),
                ]}
              >
                <Input.Password />
              </Form.Item>
              
              <Form.Item>
                <Button type="primary" htmlType="submit" loading={loading} icon={<KeyOutlined />}>
                  修改密码
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </TabPane>

        {/* 通知设置 */}
        <TabPane
          tab={
            <span>
              <BellOutlined />
              通知设置
            </span>
          }
          key="notifications"
        >
          <Card title="通知偏好">
            <Form
              layout="vertical"
              initialValues={systemSettings}
              onValuesChange={(_, allValues) => {
                setSystemSettings({ ...systemSettings, ...allValues });
              }}
            >
              <Space direction="vertical" size="large" style={{ width: '100%' }}>
                <div>
                  <Form.Item name="emailNotifications" valuePropName="checked">
                    <Space>
                      <Switch />
                      <span>邮件通知</span>
                    </Space>
                  </Form.Item>
                  <Text type="secondary">接收重要系统通知和报表更新提醒</Text>
                </div>
                
                <div>
                  <Form.Item name="smsNotifications" valuePropName="checked">
                    <Space>
                      <Switch />
                      <span>短信通知</span>
                    </Space>
                  </Form.Item>
                  <Text type="secondary">接收紧急通知和安全提醒</Text>
                </div>
                
                <div>
                  <Form.Item name="desktopNotifications" valuePropName="checked">
                    <Space>
                      <Switch />
                      <span>桌面通知</span>
                    </Space>
                  </Form.Item>
                  <Text type="secondary">在浏览器中显示实时通知</Text>
                </div>
              </Space>
            </Form>
          </Card>
        </TabPane>

        {/* 团队管理 */}
        <TabPane
          tab={
            <span>
              <TeamOutlined />
              团队管理
            </span>
          }
          key="team"
        >
          <Card
            title="团队成员"
            extra={
              <Button type="primary" icon={<PlusOutlined />} onClick={handleAddMember}>
                添加成员
              </Button>
            }
          >
            <Table
              columns={teamColumns}
              dataSource={teamMembers}
              rowKey="id"
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showTotal: (total) => `共 ${total} 个成员`,
              }}
            />
          </Card>
        </TabPane>
      </Tabs>

      {/* 添加/编辑成员模态框 */}
      <Modal
        title={editingMember ? '编辑成员' : '添加成员'}
        open={teamModalVisible}
        onOk={handleMemberModalOk}
        onCancel={() => {
          setTeamModalVisible(false);
          memberForm.resetFields();
        }}
        width={600}
      >
        <Form
          form={memberForm}
          layout="vertical"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="姓名"
                rules={[{ required: true, message: '请输入姓名' }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="email"
                label="邮箱"
                rules={[
                  { required: true, message: '请输入邮箱' },
                  { type: 'email', message: '请输入有效的邮箱地址' }
                ]}
              >
                <Input />
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="role"
                label="角色"
                rules={[{ required: true, message: '请选择角色' }]}
              >
                <Select>
                  <Option value="管理员">管理员</Option>
                  <Option value="用户">用户</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="department"
                label="部门"
                rules={[{ required: true, message: '请输入部门' }]}
              >
                <Input />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div>
  );
};

export default SettingsPage;