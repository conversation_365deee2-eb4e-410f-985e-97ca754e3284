import React, { useEffect, useState } from 'react'
import { 
  Layout, 
  Card, 
  Row, 
  Col, 
  Statistic, 
  Table, 
  Button, 
  Typography, 
  Space,
  Tag,
  Avatar,
  Dropdown,
  Menu,
  message
} from 'antd'
import { 
  DollarOutlined, 
  TrendingUpOutlined, 
  FileTextOutlined, 
  TeamOutlined,
  PlusOutlined,
  DownloadOutlined,
  EyeOutlined,
  MoreOutlined,
  UserOutlined,
  LogoutOutlined
} from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import { useAppDispatch, useAppSelector } from '../../hooks/redux'
import { logout, selectCurrentUser } from '../../store/slices/authSlice'
import { companyAPI, reportAPI } from '../../services/api'

const { Header, Content } = Layout
const { Title, Text } = Typography

interface DashboardStats {
  totalCompanies: number
  totalReports: number
  completedReports: number
  pendingReports: number
}

interface RecentReport {
  id: number
  title: string
  company: string
  status: 'completed' | 'processing' | 'failed'
  createdAt: string
}

const DashboardPage: React.FC = () => {
  const navigate = useNavigate()
  const dispatch = useAppDispatch()
  const currentUser = useAppSelector(selectCurrentUser)
  
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState<DashboardStats>({
    totalCompanies: 0,
    totalReports: 0,
    completedReports: 0,
    pendingReports: 0
  })
  const [recentReports, setRecentReports] = useState<RecentReport[]>([])

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    try {
      setLoading(true)
      
      // 加载公司和报告数据
      const [companies, reports] = await Promise.all([
        companyAPI.getCompanies(),
        reportAPI.getReports()
      ])

      // 计算统计数据
      const completedReports = reports.filter(r => r.status === 'completed').length
      const pendingReports = reports.filter(r => r.status === 'processing').length

      setStats({
        totalCompanies: companies.length,
        totalReports: reports.length,
        completedReports,
        pendingReports
      })

      // 设置最近的报告
      const recentReportsData: RecentReport[] = reports.slice(0, 5).map(report => {
        const company = companies.find(c => c.id === report.company_id)
        return {
          id: report.id,
          title: report.title,
          company: company?.name || '未知公司',
          status: report.status as 'completed' | 'processing' | 'failed',
          createdAt: report.created_at
        }
      })

      setRecentReports(recentReportsData)
    } catch (error) {
      console.error('加载仪表板数据失败:', error)
      message.error('加载数据失败，请刷新页面重试')
    } finally {
      setLoading(false)
    }
  }

  const handleLogout = () => {
    dispatch(logout())
    navigate('/login')
    message.success('已安全退出')
  }

  const userMenu = (
    <Menu>
      <Menu.Item key="profile" icon={<UserOutlined />}>
        个人资料
      </Menu.Item>
      <Menu.Divider />
      <Menu.Item key="logout" icon={<LogoutOutlined />} onClick={handleLogout}>
        退出登录
      </Menu.Item>
    </Menu>
  )

  const reportColumns = [
    {
      title: '报告名称',
      dataIndex: 'title',
      key: 'title',
      render: (text: string) => <Text strong>{text}</Text>
    },
    {
      title: '公司',
      dataIndex: 'company',
      key: 'company',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusConfig = {
          completed: { color: 'success', text: '已完成' },
          processing: { color: 'processing', text: '处理中' },
          failed: { color: 'error', text: '失败' }
        }
        const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.failed
        return <Tag color={config.color}>{config.text}</Tag>
      }
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => new Date(date).toLocaleDateString('zh-CN')
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record: RecentReport) => (
        <Space>
          <Button 
            type="link" 
            icon={<EyeOutlined />} 
            size="small"
            onClick={() => navigate(`/reports/${record.id}`)}
          >
            查看
          </Button>
          {record.status === 'completed' && (
            <Button 
              type="link" 
              icon={<DownloadOutlined />} 
              size="small"
            >
              下载
            </Button>
          )}
        </Space>
      )
    }
  ]

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Header style={{ 
        background: '#fff', 
        padding: '0 24px', 
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <div style={{
            width: 40,
            height: 40,
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'white',
            fontWeight: 'bold',
            marginRight: 16
          }}>
            LF
          </div>
          <Title level={3} style={{ margin: 0, color: '#1f2937' }}>
            LinkFin 财务分析系统
          </Title>
        </div>
        
        <Dropdown overlay={userMenu} placement="bottomRight">
          <Space style={{ cursor: 'pointer' }}>
            <Avatar icon={<UserOutlined />} />
            <Text>{currentUser?.full_name || currentUser?.username || '用户'}</Text>
          </Space>
        </Dropdown>
      </Header>

      <Content style={{ padding: '24px', background: '#f5f5f5' }}>
        <div style={{ marginBottom: 24 }}>
          <Title level={2}>仪表板</Title>
          <Text type="secondary">欢迎回来，查看您的财务分析概览</Text>
        </div>

        {/* 统计卡片 */}
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="总公司数"
                value={stats.totalCompanies}
                prefix={<TeamOutlined style={{ color: '#1890ff' }} />}
                loading={loading}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="总报告数"
                value={stats.totalReports}
                prefix={<FileTextOutlined style={{ color: '#52c41a' }} />}
                loading={loading}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="已完成报告"
                value={stats.completedReports}
                prefix={<TrendingUpOutlined style={{ color: '#722ed1' }} />}
                loading={loading}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="处理中报告"
                value={stats.pendingReports}
                prefix={<DollarOutlined style={{ color: '#fa8c16' }} />}
                loading={loading}
              />
            </Card>
          </Col>
        </Row>

        {/* 快速操作 */}
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col span={24}>
            <Card title="快速操作">
              <Space wrap>
                <Button 
                  type="primary" 
                  icon={<PlusOutlined />}
                  onClick={() => navigate('/companies/new')}
                >
                  添加公司
                </Button>
                <Button 
                  icon={<FileTextOutlined />}
                  onClick={() => navigate('/reports/new')}
                >
                  生成报告
                </Button>
                <Button 
                  icon={<TrendingUpOutlined />}
                  onClick={() => navigate('/analysis')}
                >
                  财务分析
                </Button>
              </Space>
            </Card>
          </Col>
        </Row>

        {/* 最近报告 */}
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Card 
              title="最近报告" 
              extra={
                <Button type="link" onClick={() => navigate('/reports')}>
                  查看全部
                </Button>
              }
            >
              <Table
                columns={reportColumns}
                dataSource={recentReports}
                rowKey="id"
                loading={loading}
                pagination={false}
                size="middle"
              />
            </Card>
          </Col>
        </Row>
      </Content>
    </Layout>
  )
}

export default DashboardPage
