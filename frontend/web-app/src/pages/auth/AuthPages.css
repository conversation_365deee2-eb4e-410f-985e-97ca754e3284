/* 认证页面样式 */
.auth-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.auth-card {
  width: 100%;
  max-width: 400px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.auth-header {
  text-align: center;
  margin-bottom: 32px;
}

.auth-logo {
  width: 64px;
  height: 64px;
  margin: 0 auto 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  font-weight: bold;
}

.auth-title {
  color: #1f2937;
  margin-bottom: 8px !important;
  font-weight: 600;
}

.auth-subtitle {
  color: #6b7280;
  margin-bottom: 0 !important;
}

.auth-form {
  padding: 0;
}

.auth-form .ant-form-item {
  margin-bottom: 20px;
}

.auth-form .ant-input {
  height: 44px;
  border-radius: 8px;
  border: 1px solid #d1d5db;
  font-size: 14px;
}

.auth-form .ant-input:focus,
.auth-form .ant-input-focused {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.auth-form .ant-input-prefix {
  color: #9ca3af;
  margin-right: 8px;
}

.auth-submit-btn {
  width: 100%;
  height: 44px;
  border-radius: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 16px;
}

.auth-submit-btn:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.auth-divider {
  margin: 24px 0;
}

.auth-divider .ant-divider-inner-text {
  color: #9ca3af;
  font-size: 14px;
}

.demo-buttons {
  display: flex;
  gap: 8px;
  margin-bottom: 20px;
}

.demo-btn {
  flex: 1;
  height: 36px;
  border-radius: 6px;
  font-size: 13px;
  border: 1px solid #e5e7eb;
  background: #f9fafb;
  color: #6b7280;
}

.demo-btn:hover {
  border-color: #667eea;
  color: #667eea;
  background: #f0f4ff;
}

.auth-footer {
  text-align: center;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #e5e7eb;
}

.auth-footer a {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
}

.auth-footer a:hover {
  color: #5a6fd8;
  text-decoration: underline;
}

.auth-extra {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.auth-extra .ant-checkbox-wrapper {
  color: #6b7280;
  font-size: 14px;
}

.forgot-password {
  color: #667eea;
  text-decoration: none;
  font-size: 14px;
}

.forgot-password:hover {
  color: #5a6fd8;
  text-decoration: underline;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .auth-container {
    padding: 16px;
  }
  
  .auth-card {
    max-width: 100%;
  }
  
  .demo-buttons {
    flex-direction: column;
  }
  
  .demo-btn {
    width: 100%;
  }
}

/* 加载状态 */
.auth-loading {
  position: relative;
  overflow: hidden;
}

.auth-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* 错误状态 */
.auth-form .ant-form-item-has-error .ant-input {
  border-color: #ef4444;
}

.auth-form .ant-form-item-has-error .ant-input:focus {
  border-color: #ef4444;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.1);
}

/* 成功状态 */
.auth-form .ant-form-item-has-success .ant-input {
  border-color: #10b981;
}

.auth-form .ant-form-item-has-success .ant-input:focus {
  border-color: #10b981;
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.1);
}
