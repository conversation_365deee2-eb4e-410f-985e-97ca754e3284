import React, { useEffect, useState } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Typography,
  Row,
  Col,
  Statistic,
  Tag,
  Modal,
  Form,
  message,
  Spin,
  Alert,
  Descriptions,
  Divider
} from 'antd';
import {
  DownloadOutlined,
  EyeOutlined,
  LockOutlined,
  UnlockOutlined,
  ShareAltOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import { useParams, useNavigate } from 'react-router-dom';
import { formatDate, formatCurrency } from '@/utils/format';

const { Title, Text, Paragraph } = Typography;
const { Password } = Input;

interface ShareData {
  id: string;
  title: string;
  reportId: string;
  reportTitle: string;
  companyName: string;
  companyInfo: {
    registrationNumber: string;
    address: string;
    legalRepresentative: string;
  };
  isPublic: boolean;
  requiresPassword: boolean;
  expiresAt?: string;
  viewCount: number;
  downloadCount: number;
  status: 'active' | 'expired' | 'disabled';
  createdAt: string;
  createdBy: string;
  reportData: {
    period: string;
    reportType: string;
    currency: string;
    data: Array<{
      category: string;
      items: Array<{
        name: string;
        currentPeriod: number;
        previousPeriod: number;
        note?: string;
      }>;
    }>;
  };
}

const ShareViewPage: React.FC = () => {
  const { shareId } = useParams<{ shareId: string }>();
  const navigate = useNavigate();
  const [shareData, setShareData] = useState<ShareData | null>(null);
  const [loading, setLoading] = useState(true);
  const [passwordRequired, setPasswordRequired] = useState(false);
  const [password, setPassword] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [form] = Form.useForm();

  // 模拟分享数据
  const mockShareData: ShareData = {
    id: shareId || '1',
    title: '2023年Q4财务报表分享',
    reportId: '1',
    reportTitle: '2023年第四季度资产负债表',
    companyName: '示例科技有限公司',
    companyInfo: {
      registrationNumber: '91110000123456789X',
      address: '北京市朝阳区示例大街123号',
      legalRepresentative: '张三'
    },
    isPublic: false,
    requiresPassword: true,
    expiresAt: '2024-02-15T23:59:59Z',
    viewCount: 25,
    downloadCount: 8,
    status: 'active',
    createdAt: '2024-01-15T10:00:00Z',
    createdBy: '财务部',
    reportData: {
      period: '2023年第四季度',
      reportType: '资产负债表',
      currency: 'CNY',
      data: [
        {
          category: '资产',
          items: [
            {
              name: '货币资金',
              currentPeriod: 5000000,
              previousPeriod: 4500000,
              note: '主要为银行存款'
            },
            {
              name: '应收账款',
              currentPeriod: 3200000,
              previousPeriod: 2800000
            },
            {
              name: '存货',
              currentPeriod: 2100000,
              previousPeriod: 1900000
            },
            {
              name: '固定资产',
              currentPeriod: 8500000,
              previousPeriod: 8800000,
              note: '已扣除累计折旧'
            }
          ]
        },
        {
          category: '负债',
          items: [
            {
              name: '应付账款',
              currentPeriod: 1800000,
              previousPeriod: 1600000
            },
            {
              name: '短期借款',
              currentPeriod: 2000000,
              previousPeriod: 2500000
            },
            {
              name: '应付职工薪酬',
              currentPeriod: 450000,
              previousPeriod: 420000
            }
          ]
        },
        {
          category: '所有者权益',
          items: [
            {
              name: '实收资本',
              currentPeriod: 10000000,
              previousPeriod: 10000000
            },
            {
              name: '未分配利润',
              currentPeriod: 4550000,
              previousPeriod: 3500000
            }
          ]
        }
      ]
    }
  };

  useEffect(() => {
    loadShareData();
  }, [shareId]);

  const loadShareData = async () => {
    try {
      setLoading(true);
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 检查分享是否需要密码
      if (mockShareData.requiresPassword) {
        setPasswordRequired(true);
        setLoading(false);
        return;
      }
      
      setShareData(mockShareData);
      // 增加访问计数
      // await incrementViewCount(shareId);
    } catch (error) {
      setError('加载分享数据失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const handlePasswordSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      // 模拟密码验证
      if (values.password === '123456') {
        setPasswordRequired(false);
        setShareData(mockShareData);
        message.success('验证成功');
      } else {
        message.error('密码错误，请重试');
      }
    } catch (error) {
      console.error('密码验证失败:', error);
    }
  };

  const handleDownload = async () => {
    try {
      // 这里应该调用下载API
      message.success('报表下载已开始');
      // await downloadReport(shareData.reportId);
    } catch (error) {
      message.error('下载失败，请重试');
    }
  };

  const getChangeIndicator = (current: number, previous: number) => {
    const change = current - previous;
    const changePercent = previous !== 0 ? (change / previous * 100).toFixed(2) : '0.00';
    
    if (change > 0) {
      return <Text type="success">+{changePercent}%</Text>;
    } else if (change < 0) {
      return <Text type="danger">{changePercent}%</Text>;
    } else {
      return <Text type="secondary">0.00%</Text>;
    }
  };

  const columns = [
    {
      title: '项目',
      dataIndex: 'name',
      key: 'name',
      width: '30%',
    },
    {
      title: '本期金额',
      dataIndex: 'currentPeriod',
      key: 'currentPeriod',
      width: '25%',
      render: (value: number) => formatCurrency(value),
    },
    {
      title: '上期金额',
      dataIndex: 'previousPeriod',
      key: 'previousPeriod',
      width: '25%',
      render: (value: number) => formatCurrency(value),
    },
    {
      title: '变动',
      key: 'change',
      width: '10%',
      render: (_: any, record: any) => getChangeIndicator(record.currentPeriod, record.previousPeriod),
    },
    {
      title: '备注',
      dataIndex: 'note',
      key: 'note',
      width: '10%',
      render: (note: string) => note || '-',
    },
  ];

  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        minHeight: '400px' 
      }}>
        <Spin size="large">
          <div style={{ padding: '20px', textAlign: 'center' }}>加载中...</div>
        </Spin>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ padding: '24px' }}>
        <Alert
          message="加载失败"
          description={error}
          type="error"
          showIcon
          action={
            <Button size="small" onClick={loadShareData}>
              重试
            </Button>
          }
        />
      </div>
    );
  }

  if (passwordRequired) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        minHeight: '100vh',
        background: '#f5f5f5'
      }}>
        <Card style={{ width: 400, textAlign: 'center' }}>
          <div style={{ marginBottom: '24px' }}>
            <LockOutlined style={{ fontSize: '48px', color: '#1890ff' }} />
            <Title level={3} style={{ marginTop: '16px' }}>访问受限</Title>
            <Text type="secondary">此分享需要密码才能访问</Text>
          </div>
          
          <Form form={form} onFinish={handlePasswordSubmit}>
            <Form.Item
              name="password"
              rules={[{ required: true, message: '请输入访问密码' }]}
            >
              <Password
                placeholder="请输入访问密码"
                size="large"
                onPressEnter={handlePasswordSubmit}
              />
            </Form.Item>
            
            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                size="large"
                style={{ width: '100%' }}
              >
                访问
              </Button>
            </Form.Item>
          </Form>
        </Card>
      </div>
    );
  }

  if (!shareData) {
    return (
      <div style={{ padding: '24px' }}>
        <Alert
          message="分享不存在"
          description="您访问的分享链接不存在或已失效"
          type="warning"
          showIcon
        />
      </div>
    );
  }

  return (
    <div style={{ padding: '24px', background: '#f5f5f5', minHeight: '100vh' }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        {/* 头部信息 */}
        <Card style={{ marginBottom: '24px' }}>
          <Row justify="space-between" align="middle">
            <Col>
              <Space direction="vertical" size="small">
                <Title level={2} style={{ margin: 0 }}>
                  <FileTextOutlined style={{ marginRight: '8px' }} />
                  {shareData.title}
                </Title>
                <Text type="secondary">{shareData.reportTitle}</Text>
                <Space>
                  <Tag color="blue">{shareData.reportData.reportType}</Tag>
                  <Tag color="green">{shareData.reportData.period}</Tag>
                  {!shareData.isPublic && <Tag color="orange">私有分享</Tag>}
                </Space>
              </Space>
            </Col>
            <Col>
              <Space>
                <Button
                  type="primary"
                  icon={<DownloadOutlined />}
                  onClick={handleDownload}
                >
                  下载报表
                </Button>
                <Button
                  icon={<ShareAltOutlined />}
                  onClick={() => {
                    navigator.clipboard.writeText(window.location.href);
                    message.success('链接已复制');
                  }}
                >
                  分享
                </Button>
              </Space>
            </Col>
          </Row>
        </Card>

        {/* 公司信息 */}
        <Card title="公司信息" style={{ marginBottom: '24px' }}>
          <Descriptions column={2}>
            <Descriptions.Item label="公司名称">{shareData.companyName}</Descriptions.Item>
            <Descriptions.Item label="统一社会信用代码">{shareData.companyInfo.registrationNumber}</Descriptions.Item>
            <Descriptions.Item label="法定代表人">{shareData.companyInfo.legalRepresentative}</Descriptions.Item>
            <Descriptions.Item label="注册地址">{shareData.companyInfo.address}</Descriptions.Item>
          </Descriptions>
        </Card>

        {/* 报表统计 */}
        <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
          <Col span={6}>
            <Card>
              <Statistic
                title="总资产"
                value={shareData.reportData.data[0]?.items.reduce((sum, item) => sum + item.currentPeriod, 0) || 0}
                formatter={(value) => formatCurrency(Number(value))}
                valueStyle={{ color: '#3f8600' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="总负债"
                value={shareData.reportData.data[1]?.items.reduce((sum, item) => sum + item.currentPeriod, 0) || 0}
                formatter={(value) => formatCurrency(Number(value))}
                valueStyle={{ color: '#cf1322' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="所有者权益"
                value={shareData.reportData.data[2]?.items.reduce((sum, item) => sum + item.currentPeriod, 0) || 0}
                formatter={(value) => formatCurrency(Number(value))}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="访问次数"
                value={shareData.viewCount}
                suffix="次"
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
        </Row>

        {/* 财务数据 */}
        {shareData.reportData.data.map((category, index) => (
          <Card
            key={index}
            title={category.category}
            style={{ marginBottom: '24px' }}
          >
            <Table
              columns={columns}
              dataSource={category.items}
              rowKey="name"
              pagination={false}
              size="middle"
            />
          </Card>
        ))}

        {/* 分享信息 */}
        <Card title="分享信息">
          <Descriptions column={2}>
            <Descriptions.Item label="创建者">{shareData.createdBy}</Descriptions.Item>
            <Descriptions.Item label="创建时间">{formatDate(shareData.createdAt)}</Descriptions.Item>
            <Descriptions.Item label="访问次数">{shareData.viewCount}</Descriptions.Item>
            <Descriptions.Item label="下载次数">{shareData.downloadCount}</Descriptions.Item>
            {shareData.expiresAt && (
              <Descriptions.Item label="过期时间">{formatDate(shareData.expiresAt)}</Descriptions.Item>
            )}
            <Descriptions.Item label="货币单位">{shareData.reportData.currency}</Descriptions.Item>
          </Descriptions>
        </Card>
      </div>
    </div>
  );
};

export default ShareViewPage;