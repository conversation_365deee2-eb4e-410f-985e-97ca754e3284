import React, { useEffect, useState } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Tag,
  Modal,
  Form,
  message,
  Typography,
  Row,
  Col,
  Statistic,
  Switch,
  DatePicker,
  Tooltip,
  QRCode
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
  QrcodeOutlined,
  LinkOutlined,
  ShareAltOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '@/store';
import { formatDate } from '@/utils/format';

const { Search } = Input;
const { Option } = Select;
const { Title, Text } = Typography;
const { TextArea } = Input;

interface ShareRecord {
  id: string;
  title: string;
  reportId: string;
  reportTitle: string;
  companyName: string;
  shareUrl: string;
  accessCode?: string;
  isPublic: boolean;
  requiresPassword: boolean;
  password?: string;
  expiresAt?: string;
  viewCount: number;
  downloadCount: number;
  status: 'active' | 'expired' | 'disabled';
  createdAt: string;
  updatedAt: string;
  createdBy: string;
}

const SharePage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { shares, loading } = useAppSelector((state: any) => state.share);
  const [searchText, setSearchText] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<string>('');
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingShare, setEditingShare] = useState<ShareRecord | null>(null);
  const [qrModalVisible, setQrModalVisible] = useState(false);
  const [selectedShareUrl, setSelectedShareUrl] = useState('');
  const [form] = Form.useForm();

  // 模拟数据
  const mockShares: ShareRecord[] = [
    {
      id: '1',
      title: '2023年Q4财务报表分享',
      reportId: '1',
      reportTitle: '2023年第四季度资产负债表',
      companyName: '示例科技有限公司',
      shareUrl: 'https://linkfin.com/share/abc123',
      accessCode: 'ABC123',
      isPublic: false,
      requiresPassword: true,
      password: '123456',
      expiresAt: '2024-02-15T23:59:59Z',
      viewCount: 25,
      downloadCount: 8,
      status: 'active',
      createdAt: '2024-01-15T10:00:00Z',
      updatedAt: '2024-01-15T10:00:00Z',
      createdBy: '张三'
    },
    {
      id: '2',
      title: '年度利润表公开分享',
      reportId: '2',
      reportTitle: '2023年度利润表',
      companyName: '示例科技有限公司',
      shareUrl: 'https://linkfin.com/share/def456',
      isPublic: true,
      requiresPassword: false,
      viewCount: 156,
      downloadCount: 42,
      status: 'active',
      createdAt: '2024-01-10T09:00:00Z',
      updatedAt: '2024-01-10T09:00:00Z',
      createdBy: '李四'
    },
    {
      id: '3',
      title: '现金流量表临时分享',
      reportId: '3',
      reportTitle: '2023年现金流量表',
      companyName: '创新企业集团',
      shareUrl: 'https://linkfin.com/share/ghi789',
      isPublic: false,
      requiresPassword: false,
      expiresAt: '2024-01-20T23:59:59Z',
      viewCount: 5,
      downloadCount: 1,
      status: 'expired',
      createdAt: '2024-01-05T14:30:00Z',
      updatedAt: '2024-01-05T14:30:00Z',
      createdBy: '王五'
    }
  ];

  useEffect(() => {
    // 这里应该调用实际的API获取分享数据
    // dispatch(fetchShares());
  }, [dispatch]);

  const getStatusColor = (status: string) => {
    const colorMap = {
      active: 'green',
      expired: 'red',
      disabled: 'gray'
    };
    return colorMap[status as keyof typeof colorMap] || 'default';
  };

  const getStatusText = (status: string) => {
    const textMap = {
      active: '活跃',
      expired: '已过期',
      disabled: '已禁用'
    };
    return textMap[status as keyof typeof textMap] || status;
  };

  const handleSearch = (value: string) => {
    setSearchText(value);
    // 这里应该调用搜索API
  };

  const handleCreateShare = () => {
    setEditingShare(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEditShare = (share: ShareRecord) => {
    setEditingShare(share);
    form.setFieldsValue({
      title: share.title,
      isPublic: share.isPublic,
      requiresPassword: share.requiresPassword,
      password: share.password,
      expiresAt: share.expiresAt ? new Date(share.expiresAt) : null
    });
    setIsModalVisible(true);
  };

  const handleDeleteShare = (shareId: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个分享吗？此操作不可撤销。',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          // 这里应该调用删除API
          // await dispatch(deleteShare(shareId));
          message.success('分享删除成功');
        } catch (error) {
          message.error('删除失败，请重试');
        }
      }
    });
  };

  const handleCopyUrl = (url: string) => {
    navigator.clipboard.writeText(url).then(() => {
      message.success('链接已复制到剪贴板');
    }).catch(() => {
      message.error('复制失败，请手动复制');
    });
  };

  const handleShowQR = (url: string) => {
    setSelectedShareUrl(url);
    setQrModalVisible(true);
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();
      
      if (editingShare) {
        // 更新分享
        // await dispatch(updateShare({ id: editingShare.id, data: values }));
        message.success('分享更新成功');
      } else {
        // 创建分享
        // await dispatch(createShare(values));
        message.success('分享创建成功');
      }
      
      setIsModalVisible(false);
      form.resetFields();
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const handleModalCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
  };

  const columns = [
    {
      title: '分享标题',
      dataIndex: 'title',
      key: 'title',
      render: (text: string, record: ShareRecord) => (
        <div>
          <div>{text}</div>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.reportTitle}
          </Text>
        </div>
      ),
    },
    {
      title: '公司',
      dataIndex: 'companyName',
      key: 'companyName',
    },
    {
      title: '分享类型',
      key: 'shareType',
      render: (_: any, record: ShareRecord) => (
        <Space direction="vertical" size="small">
          <Tag color={record.isPublic ? 'green' : 'orange'}>
            {record.isPublic ? '公开' : '私有'}
          </Tag>
          {record.requiresPassword && (
            <Tag color="blue">需要密码</Tag>
          )}
        </Space>
      ),
    },
    {
      title: '访问统计',
      key: 'stats',
      render: (_: any, record: ShareRecord) => (
        <Space direction="vertical" size="small">
          <Text>查看: {record.viewCount}</Text>
          <Text>下载: {record.downloadCount}</Text>
        </Space>
      ),
    },
    {
      title: '过期时间',
      dataIndex: 'expiresAt',
      key: 'expiresAt',
      render: (date: string) => date ? formatDate(date) : '永不过期',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => formatDate(date),
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: ShareRecord) => (
        <Space>
          <Tooltip title="复制链接">
            <Button
              type="link"
              icon={<CopyOutlined />}
              size="small"
              onClick={() => handleCopyUrl(record.shareUrl)}
            />
          </Tooltip>
          <Tooltip title="二维码">
            <Button
              type="link"
              icon={<QrcodeOutlined />}
              size="small"
              onClick={() => handleShowQR(record.shareUrl)}
            />
          </Tooltip>
          <Button
            type="link"
            icon={<EyeOutlined />}
            size="small"
            onClick={() => window.open(record.shareUrl, '_blank')}
          >
            预览
          </Button>
          <Button
            type="link"
            icon={<EditOutlined />}
            size="small"
            onClick={() => handleEditShare(record)}
          >
            编辑
          </Button>
          <Button
            type="link"
            icon={<DeleteOutlined />}
            size="small"
            danger
            onClick={() => handleDeleteShare(record.id)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  const filteredShares = mockShares.filter(share => {
    const matchesSearch = !searchText || 
      share.title.toLowerCase().includes(searchText.toLowerCase()) ||
      share.reportTitle.toLowerCase().includes(searchText.toLowerCase()) ||
      share.companyName.toLowerCase().includes(searchText.toLowerCase());
    const matchesStatus = !selectedStatus || share.status === selectedStatus;
    return matchesSearch && matchesStatus;
  });

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>分享管理</Title>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总分享数"
              value={mockShares.length}
              suffix="个"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="活跃分享"
              value={mockShares.filter(s => s.status === 'active').length}
              suffix="个"
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总访问量"
              value={mockShares.reduce((sum, s) => sum + s.viewCount, 0)}
              suffix="次"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总下载量"
              value={mockShares.reduce((sum, s) => sum + s.downloadCount, 0)}
              suffix="次"
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 搜索和筛选 */}
      <Card style={{ marginBottom: '24px' }}>
        <Row gutter={[16, 16]} align="middle">
          <Col span={8}>
            <Search
              placeholder="搜索分享标题、报表或公司"
              allowClear
              onSearch={handleSearch}
              style={{ width: '100%' }}
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="状态"
              allowClear
              style={{ width: '100%' }}
              value={selectedStatus}
              onChange={setSelectedStatus}
            >
              <Option value="active">活跃</Option>
              <Option value="expired">已过期</Option>
              <Option value="disabled">已禁用</Option>
            </Select>
          </Col>
          <Col span={8}></Col>
          <Col span={4}>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleCreateShare}
              style={{ width: '100%' }}
            >
              新建分享
            </Button>
          </Col>
        </Row>
      </Card>

      {/* 分享列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={filteredShares}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
          locale={{
            emptyText: '暂无分享数据'
          }}
        />
      </Card>

      {/* 创建/编辑分享模态框 */}
      <Modal
        title={editingShare ? '编辑分享' : '创建分享'}
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            isPublic: false,
            requiresPassword: false
          }}
        >
          <Form.Item
            name="title"
            label="分享标题"
            rules={[{ required: true, message: '请输入分享标题' }]}
          >
            <Input placeholder="请输入分享标题" />
          </Form.Item>

          <Form.Item
            name="isPublic"
            label="分享类型"
            valuePropName="checked"
          >
            <Switch
              checkedChildren="公开"
              unCheckedChildren="私有"
            />
          </Form.Item>

          <Form.Item
            name="requiresPassword"
            label="访问控制"
            valuePropName="checked"
          >
            <Switch
              checkedChildren="需要密码"
              unCheckedChildren="无需密码"
            />
          </Form.Item>

          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.requiresPassword !== currentValues.requiresPassword
            }
          >
            {({ getFieldValue }) =>
              getFieldValue('requiresPassword') ? (
                <Form.Item
                  name="password"
                  label="访问密码"
                  rules={[{ required: true, message: '请输入访问密码' }]}
                >
                  <Input.Password placeholder="请输入访问密码" />
                </Form.Item>
              ) : null
            }
          </Form.Item>

          <Form.Item
            name="expiresAt"
            label="过期时间"
          >
            <DatePicker
              showTime
              style={{ width: '100%' }}
              placeholder="选择过期时间（可选）"
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 二维码模态框 */}
      <Modal
        title="分享二维码"
        open={qrModalVisible}
        onCancel={() => setQrModalVisible(false)}
        footer={[
          <Button key="copy" onClick={() => handleCopyUrl(selectedShareUrl)}>
            复制链接
          </Button>,
          <Button key="close" type="primary" onClick={() => setQrModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={400}
      >
        <div style={{ textAlign: 'center', padding: '20px' }}>
          <QRCode value={selectedShareUrl} size={200} />
          <div style={{ marginTop: '16px', wordBreak: 'break-all' }}>
            <Text copyable>{selectedShareUrl}</Text>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default SharePage;