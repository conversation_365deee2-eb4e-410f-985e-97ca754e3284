import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { message } from 'antd'

// 类型定义
interface User {
  id: number
  username: string
  email: string
  full_name?: string
  is_active: boolean
  created_at: string
}

interface LoginRequest {
  username: string
  password: string
  remember?: boolean
}

interface RegisterRequest {
  username: string
  email: string
  password: string
  full_name?: string
}

interface TokenResponse {
  access_token: string
  refresh_token: string
  token_type: string
  user: User
}

interface RefreshTokenRequest {
  refresh_token: string
}

interface ChangePasswordRequest {
  current_password: string
  new_password: string
}

interface Company {
  id: number
  name: string
  code: string
  description?: string
  created_at: string
}

interface Report {
  id: number
  title: string
  company_id: number
  status: string
  created_at: string
}

interface ReportShare {
  id: number
  report_id: number
  token: string
  expires_at?: string
  password?: string
}

interface ShareSettings {
  expires_at?: string
  password?: string
  allow_download?: boolean
}

interface PaginationParams {
  page?: number
  size?: number
}

interface ApiResponse<T> {
  data: T
  message?: string
}
import { getStoredToken, removeAuthToken } from '@utils/auth'

// API基础配置
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8001'
const USE_MOCK_API = import.meta.env.VITE_USE_MOCK_API === 'true' || false // 默认使用真实API

// 模拟数据
const mockUsers: User[] = [
  {
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    full_name: '系统管理员',
    is_active: true,
    created_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 2,
    username: 'demo',
    email: '<EMAIL>',
    full_name: '演示用户',
    is_active: true,
    created_at: '2024-01-01T00:00:00Z'
  }
]

const mockCompanies: Company[] = [
  {
    id: 1,
    name: '阿里巴巴集团',
    code: 'BABA',
    description: '中国领先的电商和云计算公司',
    created_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 2,
    name: '腾讯控股',
    code: 'TCEHY',
    description: '中国领先的互联网服务公司',
    created_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 3,
    name: '比亚迪股份',
    code: 'BYD',
    description: '新能源汽车和电池制造商',
    created_at: '2024-01-01T00:00:00Z'
  }
]

const mockReports: Report[] = [
  {
    id: 1,
    title: '阿里巴巴2024年Q1财务分析报告',
    company_id: 1,
    status: 'completed',
    created_at: '2024-01-15T00:00:00Z'
  },
  {
    id: 2,
    title: '腾讯控股2024年Q1财务分析报告',
    company_id: 2,
    status: 'completed',
    created_at: '2024-01-16T00:00:00Z'
  },
  {
    id: 3,
    title: '比亚迪股份2024年Q1财务分析报告',
    company_id: 3,
    status: 'processing',
    created_at: '2024-01-17T00:00:00Z'
  }
]

// 模拟API延迟
const mockDelay = (ms: number = 500) => new Promise(resolve => setTimeout(resolve, ms))

// 模拟API响应
const mockAPI = {
  // 认证相关
  login: async (credentials: LoginRequest): Promise<TokenResponse> => {
    await mockDelay()

    const user = mockUsers.find(u =>
      u.username === credentials.username || u.email === credentials.username
    )

    if (!user || credentials.password !== 'password123') {
      throw new Error('用户名或密码错误')
    }

    const token = `mock_token_${user.id}_${Date.now()}`
    const refreshToken = `mock_refresh_${user.id}_${Date.now()}`

    return {
      access_token: token,
      refresh_token: refreshToken,
      token_type: 'bearer',
      user
    }
  },

  register: async (userData: RegisterRequest): Promise<User> => {
    await mockDelay()

    const existingUser = mockUsers.find(u =>
      u.username === userData.username || u.email === userData.email
    )

    if (existingUser) {
      throw new Error('用户名或邮箱已存在')
    }

    const newUser: User = {
      id: mockUsers.length + 1,
      username: userData.username,
      email: userData.email,
      full_name: userData.full_name || '',
      is_active: true,
      created_at: new Date().toISOString()
    }

    mockUsers.push(newUser)
    return newUser
  },

  getCurrentUser: async (): Promise<User> => {
    await mockDelay()
    return mockUsers[0] // 返回第一个用户作为当前用户
  },

  // 公司相关
  getCompanies: async (): Promise<Company[]> => {
    await mockDelay()
    return mockCompanies
  },

  getCompany: async (id: number): Promise<Company> => {
    await mockDelay()
    const company = mockCompanies.find(c => c.id === id)
    if (!company) throw new Error('公司不存在')
    return company
  },

  // 报告相关
  getReports: async (): Promise<Report[]> => {
    await mockDelay()
    return mockReports
  },

  getReport: async (id: number): Promise<Report> => {
    await mockDelay()
    const report = mockReports.find(r => r.id === id)
    if (!report) throw new Error('报告不存在')
    return report
  }
}

// 创建axios实例
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    const token = getStoredToken()
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response
  },
  async (error) => {
    const originalRequest = error.config

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true

      try {
        // 尝试刷新token
        const refreshToken = localStorage.getItem('refreshToken')
        if (refreshToken) {
          const response = await authAPI.refreshToken({ refresh_token: refreshToken })
          
          // 更新token
          localStorage.setItem('accessToken', response.access_token)
          localStorage.setItem('refreshToken', response.refresh_token)
          
          // 重试原请求
          originalRequest.headers.Authorization = `Bearer ${response.access_token}`
          return apiClient(originalRequest)
        }
      } catch (refreshError) {
        // 刷新失败，清除token并跳转到登录页
        removeAuthToken()
        localStorage.removeItem('refreshToken')
        window.location.href = '/login'
        return Promise.reject(refreshError)
      }
    }

    // 处理其他错误
    if (error.response?.status >= 500) {
      message.error('服务器错误，请稍后重试')
    } else if (error.response?.status === 403) {
      message.error('权限不足')
    } else if (error.response?.status === 404) {
      message.error('请求的资源不存在')
    }

    return Promise.reject(error)
  }
)

// 通用API方法
const api = {
  get: <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> =>
    apiClient.get(url, config).then(res => res.data),
  
  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> =>
    apiClient.post(url, data, config).then(res => res.data),
  
  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> =>
    apiClient.put(url, data, config).then(res => res.data),
  
  delete: <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> =>
    apiClient.delete(url, config).then(res => res.data),
  
  patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> =>
    apiClient.patch(url, data, config).then(res => res.data),
}

// 认证API
export const authAPI = {
  login: (credentials: LoginRequest): Promise<TokenResponse> =>
    USE_MOCK_API ? mockAPI.login(credentials) : api.post('/api/v1/auth/login', credentials),

  register: (userData: RegisterRequest): Promise<User> =>
    USE_MOCK_API ? mockAPI.register(userData) : api.post('/api/v1/auth/register', userData),

  logout: (): Promise<void> =>
    USE_MOCK_API ? Promise.resolve() : api.post('/api/v1/auth/logout'),

  refreshToken: (data: RefreshTokenRequest): Promise<TokenResponse> =>
    USE_MOCK_API ? mockAPI.login({ username: 'admin', password: 'password123' }) : api.post('/api/v1/auth/refresh', data),

  getCurrentUser: (): Promise<User> =>
    USE_MOCK_API ? mockAPI.getCurrentUser() : api.get('/api/v1/auth/me'),

  updateProfile: (userData: Partial<User>): Promise<User> =>
    USE_MOCK_API ? mockAPI.getCurrentUser() : api.put('/api/v1/auth/me', userData),

  changePassword: (data: ChangePasswordRequest): Promise<void> =>
    USE_MOCK_API ? Promise.resolve() : api.post('/api/v1/auth/change-password', data),
}

// 公司API
export const companyAPI = {
  getCompanies: (params?: PaginationParams): Promise<Company[]> =>
    USE_MOCK_API ? mockAPI.getCompanies() : api.get('/api/v1/companies', { params }),

  getCompany: (id: number): Promise<Company> =>
    USE_MOCK_API ? mockAPI.getCompany(id) : api.get(`/api/v1/companies/${id}`),

  createCompany: (data: Partial<Company>): Promise<Company> =>
    USE_MOCK_API ? mockAPI.getCompany(1) : api.post('/api/v1/companies', data),

  updateCompany: (id: number, data: Partial<Company>): Promise<Company> =>
    USE_MOCK_API ? mockAPI.getCompany(id) : api.put(`/api/v1/companies/${id}`, data),

  deleteCompany: (id: number): Promise<void> =>
    USE_MOCK_API ? Promise.resolve() : api.delete(`/api/v1/companies/${id}`),
}

// 报告API
export const reportAPI = {
  getReports: (params?: PaginationParams & { company_id?: number }): Promise<Report[]> =>
    USE_MOCK_API ? mockAPI.getReports() : api.get('/api/v1/reports', { params }),

  getReport: (id: number): Promise<Report> =>
    USE_MOCK_API ? mockAPI.getReport(id) : api.get(`/api/v1/reports/${id}`),

  createReport: (data: Partial<Report>): Promise<Report> =>
    USE_MOCK_API ? mockAPI.getReport(1) : api.post('/api/v1/reports', data),

  generateReport: (id: number): Promise<{ message: string; report_id: number }> =>
    USE_MOCK_API ? Promise.resolve({ message: '报告生成成功', report_id: id }) : api.post(`/api/v1/reports/${id}/generate`),

  deleteReport: (id: number): Promise<void> =>
    USE_MOCK_API ? Promise.resolve() : api.delete(`/api/v1/reports/${id}`),

  downloadReport: (id: number, format: 'pdf' | 'pptx' = 'pdf'): Promise<Blob> =>
    USE_MOCK_API ? Promise.resolve(new Blob(['mock pdf content'], { type: 'application/pdf' })) :
    apiClient.get(`/api/v1/reports/${id}/download?format=${format}`, {
      responseType: 'blob'
    }).then(res => res.data),
}

// 分享API
export const shareAPI = {
  getShares: (params?: PaginationParams & { report_id?: number }): Promise<ReportShare[]> =>
    api.get('/api/v1/shares', { params }),
  
  createShare: (data: { report_id: number } & ShareSettings): Promise<ReportShare> =>
    api.post('/api/v1/shares', data),
  
  getShareInfo: (token: string): Promise<any> =>
    api.get(`/api/v1/shares/${token}`),
  
  accessShare: (token: string, data?: { password?: string; email?: string }): Promise<any> =>
    api.post(`/api/v1/shares/${token}/access`, data),
  
  deleteShare: (id: number): Promise<void> =>
    api.delete(`/api/v1/shares/${id}`),
}

// 数据上传API
export const dataAPI = {
  uploadFile: (file: File, companyId: number, onProgress?: (progress: number) => void): Promise<any> => {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('company_id', companyId.toString())
    
    return apiClient.post('/api/v1/data/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          onProgress(progress)
        }
      },
    }).then(res => res.data)
  },
  
  getUploadHistory: (params?: PaginationParams): Promise<any[]> =>
    api.get('/api/v1/data/uploads', { params }),
  
  deleteUpload: (id: number): Promise<void> =>
    api.delete(`/api/v1/data/uploads/${id}`),
}

// 健康检查API
export const healthAPI = {
  check: (): Promise<any> =>
    api.get('/health'),
  
  detailed: (): Promise<any> =>
    api.get('/health/detailed'),
}

export default api
