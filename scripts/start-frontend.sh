#!/bin/bash

# LinkFin 前端启动脚本
# 用于启动前端开发服务器

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Node.js和npm
check_requirements() {
    log_info "检查环境要求..."
    
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装"
        exit 1
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [[ $NODE_VERSION -lt 16 ]]; then
        log_error "Node.js 版本需要 >= 16，当前版本: $(node --version)"
        exit 1
    fi
    
    log_success "环境检查通过"
}

# 安装依赖
install_dependencies() {
    log_info "检查并安装依赖..."
    
    cd frontend/web-app
    
    if [ ! -d "node_modules" ]; then
        log_info "安装npm依赖..."
        npm install
        log_success "依赖安装完成"
    else
        log_info "依赖已存在，检查是否需要更新..."
        npm ci --only=production --silent
    fi
    
    cd ../..
}

# 启动开发服务器
start_dev_server() {
    log_info "启动前端开发服务器..."
    
    cd frontend/web-app
    
    # 设置环境变量
    export VITE_USE_MOCK_API=true
    export VITE_API_BASE_URL=http://localhost:8001
    
    log_info "环境配置:"
    log_info "  - 使用模拟API: true"
    log_info "  - API地址: http://localhost:8001"
    
    # 启动开发服务器
    log_info "正在启动开发服务器..."
    npm run dev
}

# 主函数
main() {
    echo -e "${GREEN}"
    echo "╔══════════════════════════════════════╗"
    echo "║        LinkFin 前端开发服务器        ║"
    echo "╚══════════════════════════════════════╝"
    echo -e "${NC}"
    
    # 检查是否在项目根目录
    if [ ! -f "package.json" ] && [ ! -d "frontend/web-app" ]; then
        log_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    check_requirements
    install_dependencies
    start_dev_server
}

# 运行主函数
main "$@"
